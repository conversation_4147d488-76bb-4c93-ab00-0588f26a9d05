/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as core from "../../../../../core";
/**
 * @example
 *     {
 *         name: "name"
 *     }
 */
export interface BodyEditVoiceV1VoicesVoiceIdEditPost {
    /** The name that identifies this voice. This will be displayed in the dropdown of the website. */
    name: string;
    files?: core.FileLike[] | undefined;
    /** If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse. */
    removeBackgroundNoise?: boolean;
    /** A description of the voice. */
    description?: string;
    /** Serialized labels dictionary for the voice. */
    labels?: string;
}
