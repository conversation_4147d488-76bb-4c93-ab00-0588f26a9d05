/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as core from "../../../../../core";
/**
 * @example
 *     {}
 */
export interface BodyGetSimilarLibraryVoicesV1SimilarVoicesPost {
    audioFile?: core.FileLike | undefined;
    /** Threshold for voice similarity between provided sample and library voices. Values range from 0 to 2. The smaller the value the more similar voices will be returned. */
    similarityThreshold?: number;
    /** Number of most similar voices to return. If similarity_threshold is provided, less than this number of voices may be returned. Values range from 1 to 100. */
    topK?: number;
}
