/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../environments";
import * as core from "../../../../core";
import * as ElevenLabs from "../../../index";
import { Settings } from "../resources/settings/client/Client";
import { Ivc } from "../resources/ivc/client/Client";
import { Pvc } from "../resources/pvc/client/Client";
import { Samples } from "../resources/samples/client/Client";
export declare namespace Voices {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Access to voices created either by you or ElevenLabs.
 */
export declare class Voices {
    protected readonly _options: Voices.Options;
    protected _settings: Settings | undefined;
    protected _ivc: Ivc | undefined;
    protected _pvc: Pvc | undefined;
    protected _samples: Samples | undefined;
    constructor(_options?: Voices.Options);
    get settings(): Settings;
    get ivc(): Ivc;
    get pvc(): Pvc;
    get samples(): Samples;
    /**
     * Returns a list of all available voices for a user.
     *
     * @param {ElevenLabs.VoicesGetAllRequest} request
     * @param {Voices.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.getAll()
     */
    getAll(request?: ElevenLabs.VoicesGetAllRequest, requestOptions?: Voices.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetVoicesResponse>;
    private __getAll;
    /**
     * Gets a list of all available voices for a user with search, filtering and pagination.
     *
     * @param {ElevenLabs.VoicesSearchRequest} request
     * @param {Voices.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.search({
     *         includeTotalCount: true
     *     })
     */
    search(request?: ElevenLabs.VoicesSearchRequest, requestOptions?: Voices.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetVoicesV2Response>;
    private __search;
    /**
     * Returns metadata about a specific voice.
     *
     * @param {string} voiceId - ID of the voice to be used. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.
     * @param {ElevenLabs.VoicesGetRequest} request
     * @param {Voices.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.get("21m00Tcm4TlvDq8ikWAM")
     */
    get(voiceId: string, request?: ElevenLabs.VoicesGetRequest, requestOptions?: Voices.RequestOptions): core.HttpResponsePromise<ElevenLabs.Voice>;
    private __get;
    /**
     * Deletes a voice by its ID.
     *
     * @param {string} voiceId - ID of the voice to be used. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.
     * @param {Voices.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.delete("21m00Tcm4TlvDq8ikWAM")
     */
    delete(voiceId: string, requestOptions?: Voices.RequestOptions): core.HttpResponsePromise<ElevenLabs.DeleteVoiceResponseModel>;
    private __delete;
    /**
     * Edit a voice created by you.
     *
     * @param {string} voiceId
     * @param {ElevenLabs.BodyEditVoiceV1VoicesVoiceIdEditPost} request
     * @param {Voices.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.update("21m00Tcm4TlvDq8ikWAM", {
     *         name: "name"
     *     })
     */
    update(voiceId: string, request: ElevenLabs.BodyEditVoiceV1VoicesVoiceIdEditPost, requestOptions?: Voices.RequestOptions): core.HttpResponsePromise<ElevenLabs.EditVoiceResponseModel>;
    private __update;
    /**
     * Add a shared voice to your collection of Voices
     *
     * @param {string} publicUserId - Public user ID used to publicly identify ElevenLabs users.
     * @param {string} voiceId - ID of the voice to be used. You can use the [Get voices](/docs/api-reference/voices/search) endpoint list all the available voices.
     * @param {ElevenLabs.BodyAddSharedVoiceV1VoicesAddPublicUserIdVoiceIdPost} request
     * @param {Voices.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.share("63e06b7e7cafdc46be4d2e0b3f045940231ae058d508589653d74d1265a574ca", "21m00Tcm4TlvDq8ikWAM", {
     *         newName: "John Smith"
     *     })
     */
    share(publicUserId: string, voiceId: string, request: ElevenLabs.BodyAddSharedVoiceV1VoicesAddPublicUserIdVoiceIdPost, requestOptions?: Voices.RequestOptions): core.HttpResponsePromise<ElevenLabs.AddVoiceResponseModel>;
    private __share;
    /**
     * Retrieves a list of shared voices.
     *
     * @param {ElevenLabs.VoicesGetSharedRequest} request
     * @param {Voices.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.getShared({
     *         featured: true,
     *         readerAppEnabled: true
     *     })
     */
    getShared(request?: ElevenLabs.VoicesGetSharedRequest, requestOptions?: Voices.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetLibraryVoicesResponse>;
    private __getShared;
    /**
     * Returns a list of shared voices similar to the provided audio sample. If neither similarity_threshold nor top_k is provided, we will apply default values.
     *
     * @param {ElevenLabs.BodyGetSimilarLibraryVoicesV1SimilarVoicesPost} request
     * @param {Voices.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.findSimilarVoices({})
     */
    findSimilarVoices(request: ElevenLabs.BodyGetSimilarLibraryVoicesV1SimilarVoicesPost, requestOptions?: Voices.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetLibraryVoicesResponse>;
    private __findSimilarVoices;
}
