/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../../../../../environments";
import * as core from "../../../../../../../../../../core";
import * as ElevenLabs from "../../../../../../../../../index";
export declare namespace Audio {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Audio {
    protected readonly _options: Audio.Options;
    constructor(_options?: Audio.Options);
    /**
     * Retrieve the first 30 seconds of voice sample audio with or without noise removal.
     *
     * @param {string} voiceId - Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.
     * @param {string} sampleId - Sample ID to be used
     * @param {ElevenLabs.voices.pvc.samples.AudioGetRequest} request
     * @param {Audio.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.pvc.samples.audio.get("21m00Tcm4TlvDq8ikWAM", "VW7YKqPnjY4h39yTbx2L", {
     *         removeBackgroundNoise: true
     *     })
     */
    get(voiceId: string, sampleId: string, request?: ElevenLabs.voices.pvc.samples.AudioGetRequest, requestOptions?: Audio.RequestOptions): core.HttpResponsePromise<ElevenLabs.VoiceSamplePreviewResponseModel>;
    private __get;
}
