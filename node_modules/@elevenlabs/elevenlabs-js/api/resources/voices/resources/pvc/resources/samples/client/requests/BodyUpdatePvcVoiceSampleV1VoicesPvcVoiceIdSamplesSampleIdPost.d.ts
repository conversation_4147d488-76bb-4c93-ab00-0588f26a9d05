/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface BodyUpdatePvcVoiceSampleV1VoicesPvcVoiceIdSamplesSampleIdPost {
    /** If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse. */
    removeBackgroundNoise?: boolean;
    /** Speaker IDs to be used for PVC training. Make sure you send all the speaker IDs you want to use for PVC training in one request because the last request will override the previous ones. */
    selectedSpeakerIds?: string[];
    /** The start time of the audio to be used for PVC training. Time should be in milliseconds */
    trimStartTime?: number;
    /** The end time of the audio to be used for PVC training. Time should be in milliseconds */
    trimEndTime?: number;
}
