/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../environments";
import * as core from "../../../../../../core";
import * as ElevenLabs from "../../../../../index";
import { Samples } from "../resources/samples/client/Client";
import { Verification } from "../resources/verification/client/Client";
export declare namespace Pvc {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Pvc {
    protected readonly _options: Pvc.Options;
    protected _samples: Samples | undefined;
    protected _verification: Verification | undefined;
    constructor(_options?: Pvc.Options);
    get samples(): Samples;
    get verification(): Verification;
    /**
     * Creates a new PVC voice with metadata but no samples
     *
     * @param {ElevenLabs.voices.CreatePvcVoiceRequest} request
     * @param {Pvc.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.pvc.create({
     *         name: "John Smith",
     *         language: "en"
     *     })
     */
    create(request: ElevenLabs.voices.CreatePvcVoiceRequest, requestOptions?: Pvc.RequestOptions): core.HttpResponsePromise<ElevenLabs.AddVoiceResponseModel>;
    private __create;
    /**
     * Edit PVC voice metadata
     *
     * @param {string} voiceId - Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.
     * @param {ElevenLabs.voices.BodyEditPvcVoiceV1VoicesPvcVoiceIdPost} request
     * @param {Pvc.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.pvc.update("21m00Tcm4TlvDq8ikWAM")
     */
    update(voiceId: string, request?: ElevenLabs.voices.BodyEditPvcVoiceV1VoicesPvcVoiceIdPost, requestOptions?: Pvc.RequestOptions): core.HttpResponsePromise<ElevenLabs.AddVoiceResponseModel>;
    private __update;
    /**
     * Start PVC training process for a voice.
     *
     * @param {string} voiceId - Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.
     * @param {ElevenLabs.voices.BodyRunPvcTrainingV1VoicesPvcVoiceIdTrainPost} request
     * @param {Pvc.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.pvc.train("21m00Tcm4TlvDq8ikWAM")
     */
    train(voiceId: string, request?: ElevenLabs.voices.BodyRunPvcTrainingV1VoicesPvcVoiceIdTrainPost, requestOptions?: Pvc.RequestOptions): core.HttpResponsePromise<ElevenLabs.StartPvcVoiceTrainingResponseModel>;
    private __train;
}
