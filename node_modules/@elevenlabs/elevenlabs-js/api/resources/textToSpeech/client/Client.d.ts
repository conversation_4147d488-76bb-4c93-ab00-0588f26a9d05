/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments";
import * as core from "../../../../core";
import * as ElevenLabs from "../../../index";
export declare namespace TextToSpeech {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class TextToSpeech {
    protected readonly _options: TextToSpeech.Options;
    constructor(_options?: TextToSpeech.Options);
    /**
     * Converts text into speech using a voice of your choice and returns audio.
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     */
    convert(voiceId: string, request: ElevenLabs.TextToSpeechRequest, requestOptions?: TextToSpeech.RequestOptions): core.HttpResponsePromise<ReadableStream<Uint8Array>>;
    private __convert;
    /**
     * Generate speech from text with precise character-level timing information for audio-text synchronization.
     *
     * @param {string} voiceId - Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.
     * @param {ElevenLabs.TextToSpeechWithTimestampsRequest} request
     * @param {TextToSpeech.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.textToSpeech.convertWithTimestamps("21m00Tcm4TlvDq8ikWAM", {
     *         text: "This is a test for the API of ElevenLabs."
     *     })
     */
    convertWithTimestamps(voiceId: string, request: ElevenLabs.TextToSpeechWithTimestampsRequest, requestOptions?: TextToSpeech.RequestOptions): core.HttpResponsePromise<ElevenLabs.AudioWithTimestampsResponse>;
    private __convertWithTimestamps;
    /**
     * Converts text into speech using a voice of your choice and returns audio as an audio stream.
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     */
    stream(voiceId: string, request: ElevenLabs.StreamTextToSpeechRequest, requestOptions?: TextToSpeech.RequestOptions): core.HttpResponsePromise<ReadableStream<Uint8Array>>;
    private __stream;
    /**
     * Converts text into speech using a voice of your choice and returns a stream of JSONs containing audio as a base64 encoded string together with information on when which character was spoken.
     */
    streamWithTimestamps(voiceId: string, request: ElevenLabs.StreamTextToSpeechWithTimestampsRequest, requestOptions?: TextToSpeech.RequestOptions): core.HttpResponsePromise<core.Stream<ElevenLabs.StreamingAudioChunkWithTimestampsResponse>>;
    private __streamWithTimestamps;
}
