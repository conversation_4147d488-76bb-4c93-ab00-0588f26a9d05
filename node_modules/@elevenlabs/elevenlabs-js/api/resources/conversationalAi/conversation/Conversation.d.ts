import { EventEmitter } from "events";
import { ElevenLabsClient } from "../../../../Client";
import { AudioInterface } from "./AudioInterface";
import { ClientTools } from "./ClientTools";
import { ConversationInitiationData } from "./ConversationConfig";
import { WebSocketFactory } from "./interfaces/WebSocketInterface";
import { ConversationClient } from "./interfaces/ConversationClient";
/**
 * Conversational AI session for Node.js.
 *
 * BETA: This API is subject to change without regard to backwards compatibility.
 */
export declare class Conversation extends EventEmitter {
    private client;
    private agentId;
    private requiresAuth;
    private audioInterface;
    private clientTools;
    private config;
    private webSocketFactory;
    private conversationClient;
    private callbackAgentResponse?;
    private callbackAgentResponseCorrection?;
    private callbackUserTranscript?;
    private callbackLatencyMeasurement?;
    private ws?;
    private shouldStop;
    private conversationId?;
    private lastInterruptId;
    private inputCallback?;
    constructor(options: {
        client?: ElevenLabsClient;
        conversationClient?: ConversationClient;
        webSocketFactory?: WebSocketFactory;
        agentId: string;
        requiresAuth: boolean;
        audioInterface: AudioInterface;
        config?: ConversationInitiationData;
        clientTools?: ClientTools;
        callbackAgentResponse?: (response: string) => void;
        callbackAgentResponseCorrection?: (original: string, corrected: string) => void;
        callbackUserTranscript?: (transcript: string) => void;
        callbackLatencyMeasurement?: (latencyMs: number) => void;
    });
    /**
     * Starts the conversation session.
     *
     * Will run until `endSession` is called.
     */
    startSession(): Promise<void>;
    /**
     * Ends the conversation session and cleans up resources.
     */
    endSession(): void;
    /**
     * Send a text message from the user to the agent.
     *
     * @param text The text message to send to the agent
     */
    sendUserMessage(text: string): void;
    /**
     * Register user activity to prevent session timeout.
     *
     * This sends a ping to the orchestrator to reset the timeout timer.
     */
    registerUserActivity(): void;
    /**
     * Send a contextual update to the conversation.
     *
     * Contextual updates are non-interrupting content that is sent to the server
     * to update the conversation state without directly prompting the agent.
     *
     * @param text The contextual information to send to the conversation
     */
    sendContextualUpdate(text: string): void;
    /**
     * Get the conversation ID if available.
     *
     * @returns The conversation ID or undefined if not yet available
     */
    getConversationId(): string | undefined;
    /**
     * Check if the session is currently active.
     *
     * @returns True if the session is active
     */
    isSessionActive(): boolean;
    private _onWebSocketOpen;
    private _onWebSocketMessage;
    private _onWebSocketClose;
    private _handleMessage;
    private _getWssUrl;
    private _getSignedUrl;
}
