"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioInterface = void 0;
/**
 * AudioInterface provides an abstraction for handling audio input and output.
 *
 * This interface is designed to work with Node.js audio libraries and provides
 * the necessary methods for real-time audio processing in conversational AI applications.
 */
class AudioInterface {
}
exports.AudioInterface = AudioInterface;
