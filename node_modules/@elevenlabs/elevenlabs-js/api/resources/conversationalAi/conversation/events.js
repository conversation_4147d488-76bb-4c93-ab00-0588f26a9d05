"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientToOrchestratorEvent = void 0;
/**
 * Event types that can be sent from client to orchestrator.
 */
var ClientToOrchestratorEvent;
(function (ClientToOrchestratorEvent) {
    /** Response to a ping request. */
    ClientToOrchestratorEvent["PONG"] = "pong";
    ClientToOrchestratorEvent["CLIENT_TOOL_RESULT"] = "client_tool_result";
    ClientToOrchestratorEvent["CONVERSATION_INITIATION_CLIENT_DATA"] = "conversation_initiation_client_data";
    ClientToOrchestratorEvent["FEEDBACK"] = "feedback";
    /** Non-interrupting content that is sent to the server to update the conversation state. */
    ClientToOrchestratorEvent["CONTEXTUAL_UPDATE"] = "contextual_update";
    /** User text message. */
    ClientToOrchestratorEvent["USER_MESSAGE"] = "user_message";
    ClientToOrchestratorEvent["USER_ACTIVITY"] = "user_activity";
})(ClientToOrchestratorEvent || (exports.ClientToOrchestratorEvent = ClientToOrchestratorEvent = {}));
