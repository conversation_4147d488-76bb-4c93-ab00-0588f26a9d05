"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefaultWebSocketFactory = void 0;
const ws_1 = __importDefault(require("ws"));
class DefaultWebSocketFactory {
    create(url, options) {
        return new ws_1.default(url, options);
    }
}
exports.DefaultWebSocketFactory = DefaultWebSocketFactory;
