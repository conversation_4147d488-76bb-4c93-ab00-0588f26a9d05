/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../environments";
import * as core from "../../../../../../core";
import { Settings } from "../resources/settings/client/Client";
export declare namespace Dashboard {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Dashboard {
    protected readonly _options: Dashboard.Options;
    protected _settings: Settings | undefined;
    constructor(_options?: Dashboard.Options);
    get settings(): Settings;
}
