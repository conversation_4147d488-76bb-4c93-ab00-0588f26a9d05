"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Settings = void 0;
const environments = __importStar(require("../../../../../../../../environments"));
const core = __importStar(require("../../../../../../../../core"));
const ElevenLabs = __importStar(require("../../../../../../../index"));
const headers_1 = require("../../../../../../../../core/headers");
const serializers = __importStar(require("../../../../../../../../serialization/index"));
const errors = __importStar(require("../../../../../../../../errors/index"));
class Settings {
    constructor(_options = {}) {
        this._options = _options;
    }
    /**
     * Retrieve Convai dashboard settings for the workspace
     *
     * @param {Settings.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.dashboard.settings.get()
     */
    get(requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__get(requestOptions));
    }
    __get(requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, "v1/convai/settings/dashboard"),
                method: "GET",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.GetConvAiDashboardSettingsResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling GET /v1/convai/settings/dashboard.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * Update Convai dashboard settings for the workspace
     *
     * @param {ElevenLabs.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequest} request
     * @param {Settings.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.dashboard.settings.update()
     */
    update(request = {}, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__update(request, requestOptions));
    }
    __update() {
        return __awaiter(this, arguments, void 0, function* (request = {}, requestOptions) {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, "v1/convai/settings/dashboard"),
                method: "PATCH",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: serializers.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequest.jsonOrThrow(request, {
                    unrecognizedObjectKeys: "strip",
                }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.GetConvAiDashboardSettingsResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling PATCH /v1/convai/settings/dashboard.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
}
exports.Settings = Settings;
