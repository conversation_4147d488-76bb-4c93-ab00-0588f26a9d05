/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../../../../index";
export type PatchConvAiDashboardSettingsRequestChartsItem = ElevenLabs.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequestChartsItem.CallSuccess | ElevenLabs.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequestChartsItem.Criteria | ElevenLabs.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequestChartsItem.DataCollection;
export declare namespace PatchConvAiDashboardSettingsRequestChartsItem {
    interface CallSuccess extends ElevenLabs.DashboardCallSuccessChartModel {
        type: "call_success";
    }
    interface Criteria extends ElevenLabs.DashboardCriteriaChartModel {
        type: "criteria";
    }
    interface DataCollection extends ElevenLabs.DashboardDataCollectionChartModel {
        type: "data_collection";
    }
}
