/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../../../environments";
import * as core from "../../../../../../../../core";
import * as ElevenLabs from "../../../../../../../index";
export declare namespace Settings {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Settings {
    protected readonly _options: Settings.Options;
    constructor(_options?: Settings.Options);
    /**
     * Retrieve Convai dashboard settings for the workspace
     *
     * @param {Settings.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.dashboard.settings.get()
     */
    get(requestOptions?: Settings.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetConvAiDashboardSettingsResponseModel>;
    private __get;
    /**
     * Update Convai dashboard settings for the workspace
     *
     * @param {ElevenLabs.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequest} request
     * @param {Settings.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.dashboard.settings.update()
     */
    update(request?: ElevenLabs.conversationalAi.dashboard.PatchConvAiDashboardSettingsRequest, requestOptions?: Settings.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetConvAiDashboardSettingsResponseModel>;
    private __update;
}
