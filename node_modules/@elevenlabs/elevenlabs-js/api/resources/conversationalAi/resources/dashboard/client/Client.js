"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dashboard = void 0;
const Client_1 = require("../resources/settings/client/Client");
class Dashboard {
    constructor(_options = {}) {
        this._options = _options;
    }
    get settings() {
        var _a;
        return ((_a = this._settings) !== null && _a !== void 0 ? _a : (this._settings = new Client_1.Settings(this._options)));
    }
}
exports.Dashboard = Dashboard;
