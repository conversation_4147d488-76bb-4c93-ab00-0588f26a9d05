/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../../../index";
/**
 * @example
 *     {
 *         callName: "call_name",
 *         agentId: "agent_id",
 *         agentPhoneNumberId: "agent_phone_number_id",
 *         recipients: [{
 *                 phoneNumber: "phone_number"
 *             }]
 *     }
 */
export interface BodySubmitABatchCallRequestV1ConvaiBatchCallingSubmitPost {
    callName: string;
    agentId: string;
    agentPhoneNumberId: string;
    scheduledTimeUnix?: number;
    recipients: ElevenLabs.OutboundCallRecipient[];
}
