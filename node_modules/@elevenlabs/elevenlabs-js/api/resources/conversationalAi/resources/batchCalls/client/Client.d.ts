/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../environments";
import * as core from "../../../../../../core";
import * as ElevenLabs from "../../../../../index";
export declare namespace BatchCalls {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class BatchCalls {
    protected readonly _options: BatchCalls.Options;
    constructor(_options?: BatchCalls.Options);
    /**
     * Submit a batch call request to schedule calls for multiple recipients.
     *
     * @param {ElevenLabs.conversationalAi.BodySubmitABatchCallRequestV1ConvaiBatchCallingSubmitPost} request
     * @param {BatchCalls.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.batchCalls.create({
     *         callName: "call_name",
     *         agentId: "agent_id",
     *         agentPhoneNumberId: "agent_phone_number_id",
     *         recipients: [{
     *                 phoneNumber: "phone_number"
     *             }]
     *     })
     */
    create(request: ElevenLabs.conversationalAi.BodySubmitABatchCallRequestV1ConvaiBatchCallingSubmitPost, requestOptions?: BatchCalls.RequestOptions): core.HttpResponsePromise<ElevenLabs.BatchCallResponse>;
    private __create;
    /**
     * Get all batch calls for the current workspace.
     *
     * @param {ElevenLabs.conversationalAi.BatchCallsListRequest} request
     * @param {BatchCalls.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.batchCalls.list()
     */
    list(request?: ElevenLabs.conversationalAi.BatchCallsListRequest, requestOptions?: BatchCalls.RequestOptions): core.HttpResponsePromise<ElevenLabs.WorkspaceBatchCallsResponse>;
    private __list;
    /**
     * Get detailed information about a batch call including all recipients.
     *
     * @param {string} batchId
     * @param {BatchCalls.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.batchCalls.get("batch_id")
     */
    get(batchId: string, requestOptions?: BatchCalls.RequestOptions): core.HttpResponsePromise<ElevenLabs.BatchCallDetailedResponse>;
    private __get;
    /**
     * Cancel a running batch call and set all recipients to cancelled status.
     *
     * @param {string} batchId
     * @param {BatchCalls.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.batchCalls.cancel("batch_id")
     */
    cancel(batchId: string, requestOptions?: BatchCalls.RequestOptions): core.HttpResponsePromise<ElevenLabs.BatchCallResponse>;
    private __cancel;
    /**
     * Retry a batch call, calling failed and no-response recipients again.
     *
     * @param {string} batchId
     * @param {BatchCalls.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.batchCalls.retry("batch_id")
     */
    retry(batchId: string, requestOptions?: BatchCalls.RequestOptions): core.HttpResponsePromise<ElevenLabs.BatchCallResponse>;
    private __retry;
}
