/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../../../index";
/**
 * @example
 *     {
 *         agentId: "agent_id",
 *         agentPhoneNumberId: "agent_phone_number_id",
 *         toNumber: "to_number"
 *     }
 */
export interface BodyHandleAnOutboundCallViaTwilioV1ConvaiTwilioOutboundCallPost {
    agentId: string;
    agentPhoneNumberId: string;
    toNumber: string;
    conversationInitiationClientData?: ElevenLabs.ConversationInitiationClientDataRequestInput;
}
