/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../environments";
import * as core from "../../../../../../core";
import * as ElevenLabs from "../../../../../index";
export declare namespace Tools {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Tools {
    protected readonly _options: Tools.Options;
    constructor(_options?: Tools.Options);
    /**
     * Get all available tools available in the workspace.
     *
     * @param {Tools.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.tools.list()
     */
    list(requestOptions?: Tools.RequestOptions): core.HttpResponsePromise<ElevenLabs.ToolsResponseModel>;
    private __list;
    /**
     * Add a new tool to the available tools in the workspace.
     *
     * @param {ElevenLabs.ToolRequestModel} request
     * @param {Tools.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.tools.create({
     *         toolConfig: {
     *             type: "client",
     *             name: "name",
     *             description: "description",
     *             expectsResponse: false
     *         }
     *     })
     */
    create(request: ElevenLabs.ToolRequestModel, requestOptions?: Tools.RequestOptions): core.HttpResponsePromise<ElevenLabs.ToolResponseModel>;
    private __create;
    /**
     * Get tool that is available in the workspace.
     *
     * @param {string} toolId - ID of the requested tool.
     * @param {Tools.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.tools.get("tool_id")
     */
    get(toolId: string, requestOptions?: Tools.RequestOptions): core.HttpResponsePromise<ElevenLabs.ToolResponseModel>;
    private __get;
    /**
     * Delete tool from the workspace.
     *
     * @param {string} toolId - ID of the requested tool.
     * @param {Tools.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.tools.delete("tool_id")
     */
    delete(toolId: string, requestOptions?: Tools.RequestOptions): core.HttpResponsePromise<unknown>;
    private __delete;
    /**
     * Update tool that is available in the workspace.
     *
     * @param {string} toolId - ID of the requested tool.
     * @param {ElevenLabs.ToolRequestModel} request
     * @param {Tools.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.tools.update("tool_id", {
     *         toolConfig: {
     *             type: "client",
     *             name: "name",
     *             description: "description",
     *             expectsResponse: false
     *         }
     *     })
     */
    update(toolId: string, request: ElevenLabs.ToolRequestModel, requestOptions?: Tools.RequestOptions): core.HttpResponsePromise<ElevenLabs.ToolResponseModel>;
    private __update;
    /**
     * Get a list of agents depending on this tool
     *
     * @param {string} toolId - ID of the requested tool.
     * @param {ElevenLabs.conversationalAi.ToolsGetDependentAgentsRequest} request
     * @param {Tools.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.tools.getDependentAgents("tool_id")
     */
    getDependentAgents(toolId: string, request?: ElevenLabs.conversationalAi.ToolsGetDependentAgentsRequest, requestOptions?: Tools.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetToolDependentAgentsResponseModel>;
    private __getDependentAgents;
}
