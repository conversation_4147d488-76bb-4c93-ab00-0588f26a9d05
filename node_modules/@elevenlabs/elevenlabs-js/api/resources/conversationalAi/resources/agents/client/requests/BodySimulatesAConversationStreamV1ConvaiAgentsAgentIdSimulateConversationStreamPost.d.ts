/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../../../index";
/**
 * @example
 *     {
 *         simulationSpecification: {
 *             simulatedUserConfig: {
 *                 firstMessage: "Hello, how can I help you today?",
 *                 language: "en"
 *             }
 *         }
 *     }
 */
export interface BodySimulatesAConversationStreamV1ConvaiAgentsAgentIdSimulateConversationStreamPost {
    /** A specification detailing how the conversation should be simulated */
    simulationSpecification: ElevenLabs.ConversationSimulationSpecification;
    /** A list of evaluation criteria to test */
    extraEvaluationCriteria?: ElevenLabs.PromptEvaluationCriteria[];
    /** Maximum number of new turns to generate in the conversation simulation */
    newTurnsLimit?: number;
}
