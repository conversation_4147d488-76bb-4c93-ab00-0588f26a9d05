/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../../../environments";
import * as core from "../../../../../../../../core";
import * as ElevenLabs from "../../../../../../../index";
export declare namespace LlmUsage {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class LlmUsage {
    protected readonly _options: LlmUsage.Options;
    constructor(_options?: LlmUsage.Options);
    /**
     * Calculates expected number of LLM tokens needed for the specified agent.
     *
     * @param {string} agentId
     * @param {ElevenLabs.conversationalAi.agents.LlmUsageCalculatorRequestModel} request
     * @param {LlmUsage.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.llmUsage.calculate("agent_id")
     */
    calculate(agentId: string, request?: ElevenLabs.conversationalAi.agents.LlmUsageCalculatorRequestModel, requestOptions?: LlmUsage.RequestOptions): core.HttpResponsePromise<ElevenLabs.LlmUsageCalculatorResponseModel>;
    private __calculate;
}
