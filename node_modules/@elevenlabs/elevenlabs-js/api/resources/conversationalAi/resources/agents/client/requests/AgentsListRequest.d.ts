/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface AgentsListRequest {
    /**
     * Used for fetching next page. Cursor is returned in the response.
     */
    cursor?: string;
    /**
     * How many Agents to return at maximum. Can not exceed 100, defaults to 30.
     */
    pageSize?: number;
    /**
     * Search by agents name.
     */
    search?: string;
}
