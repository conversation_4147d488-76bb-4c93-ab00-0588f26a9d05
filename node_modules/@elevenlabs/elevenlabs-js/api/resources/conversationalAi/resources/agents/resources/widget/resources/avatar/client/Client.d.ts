/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../../../../../environments";
import * as core from "../../../../../../../../../../core";
import * as ElevenLabs from "../../../../../../../../../index";
export declare namespace Avatar {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Avatar {
    protected readonly _options: Avatar.Options;
    constructor(_options?: Avatar.Options);
    /**
     * Sets the avatar for an agent displayed in the widget
     *
     * @param {string} agentId
     * @param {ElevenLabs.conversationalAi.agents.widget.BodyPostAgentAvatarV1ConvaiAgentsAgentIdAvatarPost} request
     * @param {Avatar.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.widget.avatar.create("21m00Tcm4TlvDq8ikWAM", {
     *         avatarFile: fs.createReadStream("/path/to/your/file")
     *     })
     */
    create(agentId: string, request: ElevenLabs.conversationalAi.agents.widget.BodyPostAgentAvatarV1ConvaiAgentsAgentIdAvatarPost, requestOptions?: Avatar.RequestOptions): core.HttpResponsePromise<ElevenLabs.PostAgentAvatarResponseModel>;
    private __create;
}
