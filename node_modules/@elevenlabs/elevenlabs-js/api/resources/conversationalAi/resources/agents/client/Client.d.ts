/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../environments";
import * as core from "../../../../../../core";
import * as ElevenLabs from "../../../../../index";
import { Widget } from "../resources/widget/client/Client";
import { Link } from "../resources/link/client/Client";
import { KnowledgeBase } from "../resources/knowledgeBase/client/Client";
import { LlmUsage } from "../resources/llmUsage/client/Client";
export declare namespace Agents {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Agents {
    protected readonly _options: Agents.Options;
    protected _widget: Widget | undefined;
    protected _link: Link | undefined;
    protected _knowledgeBase: KnowledgeBase | undefined;
    protected _llmUsage: LlmUsage | undefined;
    constructor(_options?: Agents.Options);
    get widget(): Widget;
    get link(): Link;
    get knowledgeBase(): KnowledgeBase;
    get llmUsage(): LlmUsage;
    /**
     * Create an agent from a config object
     *
     * @param {ElevenLabs.conversationalAi.BodyCreateAgentV1ConvaiAgentsCreatePost} request
     * @param {Agents.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.create({
     *         conversationConfig: {}
     *     })
     */
    create(request: ElevenLabs.conversationalAi.BodyCreateAgentV1ConvaiAgentsCreatePost, requestOptions?: Agents.RequestOptions): core.HttpResponsePromise<ElevenLabs.CreateAgentResponseModel>;
    private __create;
    /**
     * Retrieve config for an agent
     *
     * @param {string} agentId - The id of an agent. This is returned on agent creation.
     * @param {Agents.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.get("21m00Tcm4TlvDq8ikWAM")
     */
    get(agentId: string, requestOptions?: Agents.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetAgentResponseModel>;
    private __get;
    /**
     * Delete an agent
     *
     * @param {string} agentId - The id of an agent. This is returned on agent creation.
     * @param {Agents.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.delete("21m00Tcm4TlvDq8ikWAM")
     */
    delete(agentId: string, requestOptions?: Agents.RequestOptions): core.HttpResponsePromise<void>;
    private __delete;
    /**
     * Patches an Agent settings
     *
     * @param {string} agentId - The id of an agent. This is returned on agent creation.
     * @param {ElevenLabs.conversationalAi.UpdateAgentRequest} request
     * @param {Agents.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.update("21m00Tcm4TlvDq8ikWAM")
     */
    update(agentId: string, request?: ElevenLabs.conversationalAi.UpdateAgentRequest, requestOptions?: Agents.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetAgentResponseModel>;
    private __update;
    /**
     * Returns a list of your agents and their metadata.
     *
     * @param {ElevenLabs.conversationalAi.AgentsListRequest} request
     * @param {Agents.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.list()
     */
    list(request?: ElevenLabs.conversationalAi.AgentsListRequest, requestOptions?: Agents.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetAgentsPageResponseModel>;
    private __list;
    /**
     * Create a new agent by duplicating an existing one
     *
     * @param {string} agentId - The id of an agent. This is returned on agent creation.
     * @param {ElevenLabs.conversationalAi.BodyDuplicateAgentV1ConvaiAgentsAgentIdDuplicatePost} request
     * @param {Agents.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.duplicate("21m00Tcm4TlvDq8ikWAM")
     */
    duplicate(agentId: string, request?: ElevenLabs.conversationalAi.BodyDuplicateAgentV1ConvaiAgentsAgentIdDuplicatePost, requestOptions?: Agents.RequestOptions): core.HttpResponsePromise<ElevenLabs.CreateAgentResponseModel>;
    private __duplicate;
    /**
     * Run a conversation between the agent and a simulated user.
     *
     * @param {string} agentId - The id of an agent. This is returned on agent creation.
     * @param {ElevenLabs.conversationalAi.BodySimulatesAConversationV1ConvaiAgentsAgentIdSimulateConversationPost} request
     * @param {Agents.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.simulateConversation("21m00Tcm4TlvDq8ikWAM", {
     *         simulationSpecification: {
     *             simulatedUserConfig: {
     *                 firstMessage: "Hello, how can I help you today?",
     *                 language: "en"
     *             }
     *         }
     *     })
     */
    simulateConversation(agentId: string, request: ElevenLabs.conversationalAi.BodySimulatesAConversationV1ConvaiAgentsAgentIdSimulateConversationPost, requestOptions?: Agents.RequestOptions): core.HttpResponsePromise<ElevenLabs.AgentSimulatedChatTestResponseModel>;
    private __simulateConversation;
    /**
     * Run a conversation between the agent and a simulated user and stream back the response. Response is streamed back as partial lists of messages that should be concatenated and once the conversation has complete a single final message with the conversation analysis will be sent.
     *
     * @param {string} agentId - The id of an agent. This is returned on agent creation.
     * @param {ElevenLabs.conversationalAi.BodySimulatesAConversationStreamV1ConvaiAgentsAgentIdSimulateConversationStreamPost} request
     * @param {Agents.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.simulateConversationStream("21m00Tcm4TlvDq8ikWAM", {
     *         simulationSpecification: {
     *             simulatedUserConfig: {
     *                 firstMessage: "Hello, how can I help you today?",
     *                 language: "en"
     *             }
     *         }
     *     })
     */
    simulateConversationStream(agentId: string, request: ElevenLabs.conversationalAi.BodySimulatesAConversationStreamV1ConvaiAgentsAgentIdSimulateConversationStreamPost, requestOptions?: Agents.RequestOptions): core.HttpResponsePromise<void>;
    private __simulateConversationStream;
}
