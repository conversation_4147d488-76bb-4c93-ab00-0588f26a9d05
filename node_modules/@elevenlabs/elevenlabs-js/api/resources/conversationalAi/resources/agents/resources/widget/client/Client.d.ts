/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../../../environments";
import * as core from "../../../../../../../../core";
import * as ElevenLabs from "../../../../../../../index";
import { Avatar } from "../resources/avatar/client/Client";
export declare namespace Widget {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Widget {
    protected readonly _options: Widget.Options;
    protected _avatar: Avatar | undefined;
    constructor(_options?: Widget.Options);
    get avatar(): Avatar;
    /**
     * Retrieve the widget configuration for an agent
     *
     * @param {string} agentId - The id of an agent. This is returned on agent creation.
     * @param {ElevenLabs.conversationalAi.agents.WidgetGetRequest} request
     * @param {Widget.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.agents.widget.get("21m00Tcm4TlvDq8ikWAM")
     */
    get(agentId: string, request?: ElevenLabs.conversationalAi.agents.WidgetGetRequest, requestOptions?: Widget.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetAgentEmbedResponseModel>;
    private __get;
}
