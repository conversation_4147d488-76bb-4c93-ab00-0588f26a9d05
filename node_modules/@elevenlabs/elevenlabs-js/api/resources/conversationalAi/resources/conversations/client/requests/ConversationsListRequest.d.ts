/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../../../index";
/**
 * @example
 *     {}
 */
export interface ConversationsListRequest {
    /**
     * Used for fetching next page. Cursor is returned in the response.
     */
    cursor?: string;
    /**
     * The id of the agent you're taking the action on.
     */
    agentId?: string;
    /**
     * The result of the success evaluation
     */
    callSuccessful?: ElevenLabs.EvaluationSuccessResult;
    /**
     * Unix timestamp (in seconds) to filter conversations up to this start date.
     */
    callStartBeforeUnix?: number;
    /**
     * Unix timestamp (in seconds) to filter conversations after to this start date.
     */
    callStartAfterUnix?: number;
    /**
     * Filter conversations by the user ID who initiated them.
     */
    userId?: string;
    /**
     * How many conversations to return at maximum. Can not exceed 100, defaults to 30.
     */
    pageSize?: number;
    /**
     * Whether to include transcript summaries in the response.
     */
    summaryMode?: ElevenLabs.conversationalAi.ConversationsListRequestSummaryMode;
}
