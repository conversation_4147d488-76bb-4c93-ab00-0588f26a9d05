/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * @example
 *     {
 *         agentId: "21m00Tcm4TlvDq8ikWAM"
 *     }
 */
export interface ConversationsGetWebrtcTokenRequest {
    /**
     * The id of the agent you're taking the action on.
     */
    agentId: string;
    /**
     * Optional custom participant name. If not provided, user ID will be used
     */
    participantName?: string;
}
