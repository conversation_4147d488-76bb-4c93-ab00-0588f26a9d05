/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../environments";
import * as core from "../../../../../../core";
import * as ElevenLabs from "../../../../../index";
import { Audio } from "../resources/audio/client/Client";
import { Feedback } from "../resources/feedback/client/Client";
export declare namespace Conversations {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Conversations {
    protected readonly _options: Conversations.Options;
    protected _audio: Audio | undefined;
    protected _feedback: Feedback | undefined;
    constructor(_options?: Conversations.Options);
    get audio(): Audio;
    get feedback(): Feedback;
    /**
     * Get a signed url to start a conversation with an agent with an agent that requires authorization
     *
     * @param {ElevenLabs.conversationalAi.ConversationsGetSignedUrlRequest} request
     * @param {Conversations.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.conversations.getSignedUrl({
     *         agentId: "21m00Tcm4TlvDq8ikWAM"
     *     })
     */
    getSignedUrl(request: ElevenLabs.conversationalAi.ConversationsGetSignedUrlRequest, requestOptions?: Conversations.RequestOptions): core.HttpResponsePromise<ElevenLabs.ConversationSignedUrlResponseModel>;
    private __getSignedUrl;
    /**
     * Get a WebRTC session token for real-time communication.
     *
     * @param {ElevenLabs.conversationalAi.ConversationsGetWebrtcTokenRequest} request
     * @param {Conversations.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.conversations.getWebrtcToken({
     *         agentId: "21m00Tcm4TlvDq8ikWAM"
     *     })
     */
    getWebrtcToken(request: ElevenLabs.conversationalAi.ConversationsGetWebrtcTokenRequest, requestOptions?: Conversations.RequestOptions): core.HttpResponsePromise<ElevenLabs.TokenResponseModel>;
    private __getWebrtcToken;
    /**
     * Get all conversations of agents that user owns. With option to restrict to a specific agent.
     *
     * @param {ElevenLabs.conversationalAi.ConversationsListRequest} request
     * @param {Conversations.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.conversations.list()
     */
    list(request?: ElevenLabs.conversationalAi.ConversationsListRequest, requestOptions?: Conversations.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetConversationsPageResponseModel>;
    private __list;
    /**
     * Get the details of a particular conversation
     *
     * @param {string} conversationId - The id of the conversation you're taking the action on.
     * @param {Conversations.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.conversations.get("123")
     */
    get(conversationId: string, requestOptions?: Conversations.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetConversationResponseModel>;
    private __get;
    /**
     * Delete a particular conversation
     *
     * @param {string} conversationId - The id of the conversation you're taking the action on.
     * @param {Conversations.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.conversations.delete("21m00Tcm4TlvDq8ikWAM")
     */
    delete(conversationId: string, requestOptions?: Conversations.RequestOptions): core.HttpResponsePromise<unknown>;
    private __delete;
}
