/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../environments";
import * as core from "../../../../../../core";
import * as ElevenLabs from "../../../../../index";
export declare namespace Secrets {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Secrets {
    protected readonly _options: Secrets.Options;
    constructor(_options?: Secrets.Options);
    /**
     * Get all workspace secrets for the user
     *
     * @param {Secrets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.secrets.list()
     */
    list(requestOptions?: Secrets.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetWorkspaceSecretsResponseModel>;
    private __list;
    /**
     * Create a new secret for the workspace
     *
     * @param {ElevenLabs.conversationalAi.PostWorkspaceSecretRequest} request
     * @param {Secrets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.secrets.create({
     *         name: "name",
     *         value: "value"
     *     })
     */
    create(request: ElevenLabs.conversationalAi.PostWorkspaceSecretRequest, requestOptions?: Secrets.RequestOptions): core.HttpResponsePromise<ElevenLabs.PostWorkspaceSecretResponseModel>;
    private __create;
    /**
     * Delete a workspace secret if it's not in use
     *
     * @param {string} secretId
     * @param {Secrets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.secrets.delete("secret_id")
     */
    delete(secretId: string, requestOptions?: Secrets.RequestOptions): core.HttpResponsePromise<void>;
    private __delete;
}
