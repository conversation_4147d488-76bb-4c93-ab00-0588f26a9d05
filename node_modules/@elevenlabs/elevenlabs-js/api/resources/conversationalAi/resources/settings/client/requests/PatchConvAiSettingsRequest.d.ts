/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../../../index";
/**
 * @example
 *     {}
 */
export interface PatchConvAiSettingsRequest {
    conversationInitiationClientDataWebhook?: ElevenLabs.ConversationInitiationClientDataWebhook;
    webhooks?: ElevenLabs.ConvAiWebhooks;
    /** Whether the workspace can use MCP servers */
    canUseMcpServers?: boolean;
    ragRetentionPeriodDays?: number;
}
