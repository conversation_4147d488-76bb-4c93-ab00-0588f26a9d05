/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments";
import * as core from "../../../../core";
import * as ElevenLabs from "../../../index";
import { Conversations } from "../resources/conversations/client/Client";
import { <PERSON><PERSON><PERSON> } from "../resources/twilio/client/Client";
import { Agents } from "../resources/agents/client/Client";
import { PhoneNumbers } from "../resources/phoneNumbers/client/Client";
import { LlmUsage } from "../resources/llmUsage/client/Client";
import { KnowledgeBase } from "../resources/knowledgeBase/client/Client";
import { Tools } from "../resources/tools/client/Client";
import { Settings } from "../resources/settings/client/Client";
import { Secrets } from "../resources/secrets/client/Client";
import { BatchCalls } from "../resources/batchCalls/client/Client";
import { SipTrunk } from "../resources/sipTrunk/client/Client";
import { McpServers } from "../resources/mcpServers/client/Client";
import { Dashboard } from "../resources/dashboard/client/Client";
export declare namespace ConversationalAi {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class ConversationalAi {
    protected readonly _options: ConversationalAi.Options;
    protected _conversations: Conversations | undefined;
    protected _twilio: Twilio | undefined;
    protected _agents: Agents | undefined;
    protected _phoneNumbers: PhoneNumbers | undefined;
    protected _llmUsage: LlmUsage | undefined;
    protected _knowledgeBase: KnowledgeBase | undefined;
    protected _tools: Tools | undefined;
    protected _settings: Settings | undefined;
    protected _secrets: Secrets | undefined;
    protected _batchCalls: BatchCalls | undefined;
    protected _sipTrunk: SipTrunk | undefined;
    protected _mcpServers: McpServers | undefined;
    protected _dashboard: Dashboard | undefined;
    constructor(_options?: ConversationalAi.Options);
    get conversations(): Conversations;
    get twilio(): Twilio;
    get agents(): Agents;
    get phoneNumbers(): PhoneNumbers;
    get llmUsage(): LlmUsage;
    get knowledgeBase(): KnowledgeBase;
    get tools(): Tools;
    get settings(): Settings;
    get secrets(): Secrets;
    get batchCalls(): BatchCalls;
    get sipTrunk(): SipTrunk;
    get mcpServers(): McpServers;
    get dashboard(): Dashboard;
    /**
     * Upload a file or webpage URL to create a knowledge base document. <br> <Note> After creating the document, update the agent's knowledge base by calling [Update agent](/docs/api-reference/agents/update). </Note>
     *
     * @param {ElevenLabs.BodyAddToKnowledgeBaseV1ConvaiKnowledgeBasePost} request
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.addToKnowledgeBase({})
     */
    addToKnowledgeBase(request: ElevenLabs.BodyAddToKnowledgeBaseV1ConvaiKnowledgeBasePost, requestOptions?: ConversationalAi.RequestOptions): core.HttpResponsePromise<ElevenLabs.AddKnowledgeBaseResponseModel>;
    private __addToKnowledgeBase;
    /**
     * Provides information about all RAG indexes of the specified knowledgebase document.
     *
     * @param {string} documentationId - The id of a document from the knowledge base. This is returned on document addition.
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.getDocumentRagIndexes("21m00Tcm4TlvDq8ikWAM")
     */
    getDocumentRagIndexes(documentationId: string, requestOptions?: ConversationalAi.RequestOptions): core.HttpResponsePromise<ElevenLabs.RagDocumentIndexesResponseModel>;
    private __getDocumentRagIndexes;
    /**
     * Delete RAG index for the knowledgebase document.
     *
     * @param {string} documentationId - The id of a document from the knowledge base. This is returned on document addition.
     * @param {string} ragIndexId - The id of RAG index of document from the knowledge base.
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.deleteDocumentRagIndex("21m00Tcm4TlvDq8ikWAM", "21m00Tcm4TlvDq8ikWAM")
     */
    deleteDocumentRagIndex(documentationId: string, ragIndexId: string, requestOptions?: ConversationalAi.RequestOptions): core.HttpResponsePromise<ElevenLabs.RagDocumentIndexResponseModel>;
    private __deleteDocumentRagIndex;
    /**
     * Provides total size and other information of RAG indexes used by knowledgebase documents
     *
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.ragIndexOverview()
     */
    ragIndexOverview(requestOptions?: ConversationalAi.RequestOptions): core.HttpResponsePromise<ElevenLabs.RagIndexOverviewResponseModel>;
    private __ragIndexOverview;
    /**
     * Update an existing secret for the workspace
     *
     * @param {string} secretId
     * @param {ElevenLabs.PatchWorkspaceSecretRequest} request
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.updateSecret("secret_id", {
     *         name: "name",
     *         value: "value"
     *     })
     */
    updateSecret(secretId: string, request: ElevenLabs.PatchWorkspaceSecretRequest, requestOptions?: ConversationalAi.RequestOptions): core.HttpResponsePromise<ElevenLabs.PostWorkspaceSecretResponseModel>;
    private __updateSecret;
}
