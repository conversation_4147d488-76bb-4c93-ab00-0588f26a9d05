/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as core from "../../../../../core";
/**
 * @example
 *     {}
 */
export interface BodyAddToKnowledgeBaseV1ConvaiKnowledgeBasePost {
    agentId?: string;
    /** A custom, human-readable name for the document. */
    name?: string;
    /** URL to a page of documentation that the agent will have access to in order to interact with users. */
    url?: string;
    file?: core.FileLike | undefined;
}
