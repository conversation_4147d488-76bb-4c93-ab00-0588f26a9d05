"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationalAi = void 0;
const environments = __importStar(require("../../../../environments"));
const core = __importStar(require("../../../../core"));
const ElevenLabs = __importStar(require("../../../index"));
const headers_1 = require("../../../../core/headers");
const serializers = __importStar(require("../../../../serialization/index"));
const errors = __importStar(require("../../../../errors/index"));
const Client_1 = require("../resources/conversations/client/Client");
const Client_2 = require("../resources/twilio/client/Client");
const Client_3 = require("../resources/agents/client/Client");
const Client_4 = require("../resources/phoneNumbers/client/Client");
const Client_5 = require("../resources/llmUsage/client/Client");
const Client_6 = require("../resources/knowledgeBase/client/Client");
const Client_7 = require("../resources/tools/client/Client");
const Client_8 = require("../resources/settings/client/Client");
const Client_9 = require("../resources/secrets/client/Client");
const Client_10 = require("../resources/batchCalls/client/Client");
const Client_11 = require("../resources/sipTrunk/client/Client");
const Client_12 = require("../resources/mcpServers/client/Client");
const Client_13 = require("../resources/dashboard/client/Client");
class ConversationalAi {
    constructor(_options = {}) {
        this._options = _options;
    }
    get conversations() {
        var _a;
        return ((_a = this._conversations) !== null && _a !== void 0 ? _a : (this._conversations = new Client_1.Conversations(this._options)));
    }
    get twilio() {
        var _a;
        return ((_a = this._twilio) !== null && _a !== void 0 ? _a : (this._twilio = new Client_2.Twilio(this._options)));
    }
    get agents() {
        var _a;
        return ((_a = this._agents) !== null && _a !== void 0 ? _a : (this._agents = new Client_3.Agents(this._options)));
    }
    get phoneNumbers() {
        var _a;
        return ((_a = this._phoneNumbers) !== null && _a !== void 0 ? _a : (this._phoneNumbers = new Client_4.PhoneNumbers(this._options)));
    }
    get llmUsage() {
        var _a;
        return ((_a = this._llmUsage) !== null && _a !== void 0 ? _a : (this._llmUsage = new Client_5.LlmUsage(this._options)));
    }
    get knowledgeBase() {
        var _a;
        return ((_a = this._knowledgeBase) !== null && _a !== void 0 ? _a : (this._knowledgeBase = new Client_6.KnowledgeBase(this._options)));
    }
    get tools() {
        var _a;
        return ((_a = this._tools) !== null && _a !== void 0 ? _a : (this._tools = new Client_7.Tools(this._options)));
    }
    get settings() {
        var _a;
        return ((_a = this._settings) !== null && _a !== void 0 ? _a : (this._settings = new Client_8.Settings(this._options)));
    }
    get secrets() {
        var _a;
        return ((_a = this._secrets) !== null && _a !== void 0 ? _a : (this._secrets = new Client_9.Secrets(this._options)));
    }
    get batchCalls() {
        var _a;
        return ((_a = this._batchCalls) !== null && _a !== void 0 ? _a : (this._batchCalls = new Client_10.BatchCalls(this._options)));
    }
    get sipTrunk() {
        var _a;
        return ((_a = this._sipTrunk) !== null && _a !== void 0 ? _a : (this._sipTrunk = new Client_11.SipTrunk(this._options)));
    }
    get mcpServers() {
        var _a;
        return ((_a = this._mcpServers) !== null && _a !== void 0 ? _a : (this._mcpServers = new Client_12.McpServers(this._options)));
    }
    get dashboard() {
        var _a;
        return ((_a = this._dashboard) !== null && _a !== void 0 ? _a : (this._dashboard = new Client_13.Dashboard(this._options)));
    }
    /**
     * Upload a file or webpage URL to create a knowledge base document. <br> <Note> After creating the document, update the agent's knowledge base by calling [Update agent](/docs/api-reference/agents/update). </Note>
     *
     * @param {ElevenLabs.BodyAddToKnowledgeBaseV1ConvaiKnowledgeBasePost} request
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.addToKnowledgeBase({})
     */
    addToKnowledgeBase(request, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__addToKnowledgeBase(request, requestOptions));
    }
    __addToKnowledgeBase(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _queryParams = {};
            if (request.agentId != null) {
                _queryParams["agent_id"] = request.agentId;
            }
            const _request = yield core.newFormData();
            if (request.name != null) {
                _request.append("name", request.name);
            }
            if (request.url != null) {
                _request.append("url", request.url);
            }
            if (request.file != null) {
                yield _request.appendFile("file", request.file);
            }
            const _maybeEncodedRequest = yield _request.getRequest();
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, "v1/convai/knowledge-base"),
                method: "POST",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)(Object.assign({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }, _maybeEncodedRequest.headers)), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                queryParameters: _queryParams,
                requestType: "file",
                duplex: _maybeEncodedRequest.duplex,
                body: _maybeEncodedRequest.body,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.AddKnowledgeBaseResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling POST /v1/convai/knowledge-base.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * Provides information about all RAG indexes of the specified knowledgebase document.
     *
     * @param {string} documentationId - The id of a document from the knowledge base. This is returned on document addition.
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.getDocumentRagIndexes("21m00Tcm4TlvDq8ikWAM")
     */
    getDocumentRagIndexes(documentationId, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__getDocumentRagIndexes(documentationId, requestOptions));
    }
    __getDocumentRagIndexes(documentationId, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, `v1/convai/knowledge-base/${encodeURIComponent(documentationId)}/rag-index`),
                method: "GET",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.RagDocumentIndexesResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling GET /v1/convai/knowledge-base/{documentation_id}/rag-index.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * Delete RAG index for the knowledgebase document.
     *
     * @param {string} documentationId - The id of a document from the knowledge base. This is returned on document addition.
     * @param {string} ragIndexId - The id of RAG index of document from the knowledge base.
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.deleteDocumentRagIndex("21m00Tcm4TlvDq8ikWAM", "21m00Tcm4TlvDq8ikWAM")
     */
    deleteDocumentRagIndex(documentationId, ragIndexId, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__deleteDocumentRagIndex(documentationId, ragIndexId, requestOptions));
    }
    __deleteDocumentRagIndex(documentationId, ragIndexId, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, `v1/convai/knowledge-base/${encodeURIComponent(documentationId)}/rag-index/${encodeURIComponent(ragIndexId)}`),
                method: "DELETE",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.RagDocumentIndexResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling DELETE /v1/convai/knowledge-base/{documentation_id}/rag-index/{rag_index_id}.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * Provides total size and other information of RAG indexes used by knowledgebase documents
     *
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.ragIndexOverview()
     */
    ragIndexOverview(requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__ragIndexOverview(requestOptions));
    }
    __ragIndexOverview(requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, "v1/convai/knowledge-base/rag-index"),
                method: "GET",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.RagIndexOverviewResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling GET /v1/convai/knowledge-base/rag-index.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * Update an existing secret for the workspace
     *
     * @param {string} secretId
     * @param {ElevenLabs.PatchWorkspaceSecretRequest} request
     * @param {ConversationalAi.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.conversationalAi.updateSecret("secret_id", {
     *         name: "name",
     *         value: "value"
     *     })
     */
    updateSecret(secretId, request, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__updateSecret(secretId, request, requestOptions));
    }
    __updateSecret(secretId, request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, `v1/convai/secrets/${encodeURIComponent(secretId)}`),
                method: "PATCH",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                requestType: "json",
                body: Object.assign(Object.assign({}, serializers.PatchWorkspaceSecretRequest.jsonOrThrow(request, { unrecognizedObjectKeys: "strip" })), { type: "update" }),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.PostWorkspaceSecretResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling PATCH /v1/convai/secrets/{secret_id}.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
}
exports.ConversationalAi = ConversationalAi;
