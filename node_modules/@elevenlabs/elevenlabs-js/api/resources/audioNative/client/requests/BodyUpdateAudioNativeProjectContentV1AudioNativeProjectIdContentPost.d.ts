/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as core from "../../../../../core";
/**
 * @example
 *     {}
 */
export interface BodyUpdateAudioNativeProjectContentV1AudioNativeProjectIdContentPost {
    file?: core.FileLike | undefined;
    /** Whether to auto convert the project to audio or not. */
    autoConvert?: boolean;
    /** Whether to auto publish the new project snapshot after it's converted. */
    autoPublish?: boolean;
}
