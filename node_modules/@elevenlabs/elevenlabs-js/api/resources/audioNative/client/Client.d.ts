/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments";
import * as core from "../../../../core";
import * as ElevenLabs from "../../../index";
export declare namespace AudioNative {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class AudioNative {
    protected readonly _options: AudioNative.Options;
    constructor(_options?: AudioNative.Options);
    /**
     * Creates Audio Native enabled project, optionally starts conversion and returns project ID and embeddable HTML snippet.
     *
     * @param {ElevenLabs.BodyCreatesAudioNativeEnabledProjectV1AudioNativePost} request
     * @param {AudioNative.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.audioNative.create({
     *         name: "name"
     *     })
     */
    create(request: ElevenLabs.BodyCreatesAudioNativeEnabledProjectV1AudioNativePost, requestOptions?: AudioNative.RequestOptions): core.HttpResponsePromise<ElevenLabs.AudioNativeCreateProjectResponseModel>;
    private __create;
    /**
     * Get player settings for the specific project.
     *
     * @param {string} projectId - The ID of the Studio project.
     * @param {AudioNative.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.audioNative.getSettings("21m00Tcm4TlvDq8ikWAM")
     */
    getSettings(projectId: string, requestOptions?: AudioNative.RequestOptions): core.HttpResponsePromise<ElevenLabs.GetAudioNativeProjectSettingsResponseModel>;
    private __getSettings;
    /**
     * Updates content for the specific AudioNative Project.
     *
     * @param {string} projectId
     * @param {ElevenLabs.BodyUpdateAudioNativeProjectContentV1AudioNativeProjectIdContentPost} request
     * @param {AudioNative.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.audioNative.update("21m00Tcm4TlvDq8ikWAM", {})
     */
    update(projectId: string, request: ElevenLabs.BodyUpdateAudioNativeProjectContentV1AudioNativeProjectIdContentPost, requestOptions?: AudioNative.RequestOptions): core.HttpResponsePromise<ElevenLabs.AudioNativeEditContentResponseModel>;
    private __update;
}
