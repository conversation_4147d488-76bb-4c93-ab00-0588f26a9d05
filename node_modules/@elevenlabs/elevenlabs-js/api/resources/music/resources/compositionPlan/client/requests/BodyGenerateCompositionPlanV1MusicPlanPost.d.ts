/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../../../index";
/**
 * @example
 *     {
 *         prompt: "prompt"
 *     }
 */
export interface BodyGenerateCompositionPlanV1MusicPlanPost {
    /** A simple text prompt to compose a plan from. */
    prompt: string;
    /** The length of the composition plan to generate in milliseconds. Must be between 10000ms and 300000ms. Optional - if not provided, the model will choose a length based on the prompt. */
    musicLengthMs?: number;
    /** An optional composition plan to use as a source for the new composition plan. */
    sourceCompositionPlan?: ElevenLabs.MusicPrompt;
    /** The model to use for the generation. */
    modelId?: "music_v1";
}
