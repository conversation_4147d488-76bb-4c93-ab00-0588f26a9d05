"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Usage = void 0;
const environments = __importStar(require("../../../../environments"));
const core = __importStar(require("../../../../core"));
const ElevenLabs = __importStar(require("../../../index"));
const serializers = __importStar(require("../../../../serialization/index"));
const headers_1 = require("../../../../core/headers");
const errors = __importStar(require("../../../../errors/index"));
class Usage {
    constructor(_options = {}) {
        this._options = _options;
    }
    /**
     * Returns the usage metrics for the current user or the entire workspace they are part of. The response provides a time axis based on the specified aggregation interval (default: day), with usage values for each interval along that axis. Usage is broken down by the selected breakdown type. For example, breakdown type "voice" will return the usage of each voice for each interval along the time axis.
     *
     * @param {ElevenLabs.UsageGetRequest} request
     * @param {Usage.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.usage.get({
     *         startUnix: 1,
     *         endUnix: 1
     *     })
     */
    get(request, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__get(request, requestOptions));
    }
    __get(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const { startUnix, endUnix, includeWorkspaceMetrics, breakdownType, aggregationInterval, aggregationBucketSize, metric, } = request;
            const _queryParams = {};
            _queryParams["start_unix"] = startUnix.toString();
            _queryParams["end_unix"] = endUnix.toString();
            if (includeWorkspaceMetrics != null) {
                _queryParams["include_workspace_metrics"] = includeWorkspaceMetrics.toString();
            }
            if (breakdownType != null) {
                _queryParams["breakdown_type"] = serializers.BreakdownTypes.jsonOrThrow(breakdownType, {
                    unrecognizedObjectKeys: "strip",
                });
            }
            if (aggregationInterval != null) {
                _queryParams["aggregation_interval"] = serializers.UsageAggregationInterval.jsonOrThrow(aggregationInterval, { unrecognizedObjectKeys: "strip" });
            }
            if (aggregationBucketSize != null) {
                _queryParams["aggregation_bucket_size"] = aggregationBucketSize.toString();
            }
            if (metric != null) {
                _queryParams["metric"] = serializers.MetricType.jsonOrThrow(metric, { unrecognizedObjectKeys: "strip" });
            }
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, "v1/usage/character-stats"),
                method: "GET",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                queryParameters: _queryParams,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.UsageCharactersResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling GET /v1/usage/character-stats.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
}
exports.Usage = Usage;
