/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../index";
/**
 * @example
 *     {
 *         text: "Spacious braam suitable for high-impact movie trailer moments"
 *     }
 */
export interface CreateSoundEffectRequest {
    /**
     * Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.
     */
    outputFormat?: ElevenLabs.TextToSoundEffectsConvertRequestOutputFormat;
    /** The text that will get converted into a sound effect. */
    text: string;
    /** The duration of the sound which will be generated in seconds. Must be at least 0.5 and at most 22. If set to None we will guess the optimal duration using the prompt. Defaults to None. */
    durationSeconds?: number;
    /** A higher prompt influence makes your generation follow the prompt more closely while also making generations less variable. Must be a value between 0 and 1. Defaults to 0.3. */
    promptInfluence?: number;
}
