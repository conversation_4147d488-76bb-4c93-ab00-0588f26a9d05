/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../index";
/**
 * The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.
 */
export type BodyCreatePodcastV1StudioPodcastsPostMode = ElevenLabs.BodyCreatePodcastV1StudioPodcastsPostMode.Conversation | ElevenLabs.BodyCreatePodcastV1StudioPodcastsPostMode.Bulletin;
export declare namespace BodyCreatePodcastV1StudioPodcastsPostMode {
    interface Conversation extends ElevenLabs.PodcastConversationMode {
        type: "conversation";
    }
    interface Bulletin extends ElevenLabs.PodcastBulletinMode {
        type: "bulletin";
    }
}
