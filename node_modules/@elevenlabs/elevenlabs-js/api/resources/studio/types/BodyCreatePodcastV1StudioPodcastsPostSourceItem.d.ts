/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../index";
export type BodyCreatePodcastV1StudioPodcastsPostSourceItem = ElevenLabs.BodyCreatePodcastV1StudioPodcastsPostSourceItem.Text | ElevenLabs.BodyCreatePodcastV1StudioPodcastsPostSourceItem.Url;
export declare namespace BodyCreatePodcastV1StudioPodcastsPostSourceItem {
    interface Text extends ElevenLabs.PodcastTextSource {
        type: "text";
    }
    interface Url extends ElevenLabs.PodcastUrlSource {
        type: "url";
    }
}
