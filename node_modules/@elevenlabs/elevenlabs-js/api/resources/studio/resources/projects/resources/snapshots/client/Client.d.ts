/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../../../environments";
import * as core from "../../../../../../../../core";
import * as ElevenLabs from "../../../../../../../index";
export declare namespace Snapshots {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
export declare class Snapshots {
    protected readonly _options: Snapshots.Options;
    constructor(_options?: Snapshots.Options);
    /**
     * Retrieves a list of snapshots for a Studio project.
     *
     * @param {string} projectId - The ID of the Studio project.
     * @param {Snapshots.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.studio.projects.snapshots.list("21m00Tcm4TlvDq8ikWAM")
     */
    list(projectId: string, requestOptions?: Snapshots.RequestOptions): core.HttpResponsePromise<ElevenLabs.ProjectSnapshotsResponse>;
    private __list;
    /**
     * Returns the project snapshot.
     *
     * @param {string} projectId - The ID of the Studio project.
     * @param {string} projectSnapshotId - The ID of the Studio project snapshot.
     * @param {Snapshots.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.studio.projects.snapshots.get("21m00Tcm4TlvDq8ikWAM", "21m00Tcm4TlvDq8ikWAM")
     */
    get(projectId: string, projectSnapshotId: string, requestOptions?: Snapshots.RequestOptions): core.HttpResponsePromise<ElevenLabs.ProjectSnapshotExtendedResponseModel>;
    private __get;
    /**
     * Stream the audio from a Studio project snapshot.
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     */
    stream(projectId: string, projectSnapshotId: string, request?: ElevenLabs.studio.projects.BodyStreamStudioProjectAudioV1StudioProjectsProjectIdSnapshotsProjectSnapshotIdStreamPost, requestOptions?: Snapshots.RequestOptions): core.HttpResponsePromise<ReadableStream<Uint8Array>>;
    private __stream;
    /**
     * Returns a compressed archive of the Studio project's audio.
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     */
    streamArchive(projectId: string, projectSnapshotId: string, requestOptions?: Snapshots.RequestOptions): core.HttpResponsePromise<ReadableStream<Uint8Array>>;
    private __streamArchive;
}
