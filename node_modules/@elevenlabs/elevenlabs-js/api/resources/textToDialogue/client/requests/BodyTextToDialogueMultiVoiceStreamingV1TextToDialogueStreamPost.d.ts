/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../../../../index";
/**
 * @example
 *     {
 *         inputs: [{
 *                 text: "Knock knock",
 *                 voiceId: "JBFqnCBsd6RMkjVDRZzb"
 *             }, {
 *                 text: "Who is there?",
 *                 voiceId: "Aw4FAjKCGjjNkVhN1Xmq"
 *             }]
 *     }
 */
export interface BodyTextToDialogueMultiVoiceStreamingV1TextToDialogueStreamPost {
    /**
     * Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.
     */
    outputFormat?: ElevenLabs.TextToDialogueStreamRequestOutputFormat;
    /** A list of dialogue inputs, each containing text and a voice ID which will be converted into speech. */
    inputs: ElevenLabs.DialogueInput[];
    /** Identifier of the model that will be used, you can query them using GET /v1/models. The model needs to have support for text to speech, you can check this using the can_do_text_to_speech property. */
    modelId?: string;
    /** Settings controlling the dialogue generation. */
    settings?: ElevenLabs.ModelSettingsResponseModel;
    /** A list of pronunciation dictionary locators (id, version_id) to be applied to the text. They will be applied in order. You may have up to 3 locators per request */
    pronunciationDictionaryLocators?: ElevenLabs.PronunciationDictionaryVersionLocator[];
    /** If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be integer between 0 and 4294967295. */
    seed?: number;
}
