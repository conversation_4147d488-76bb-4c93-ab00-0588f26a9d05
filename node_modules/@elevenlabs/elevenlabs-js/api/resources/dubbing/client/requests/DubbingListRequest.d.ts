/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../index";
/**
 * @example
 *     {}
 */
export interface DubbingListRequest {
    /**
     * Used for fetching next page. Cursor is returned in the response.
     */
    cursor?: string;
    /**
     * How many dubs to return at maximum. Can not exceed 200, defaults to 100.
     */
    pageSize?: number;
    /**
     * What state the dub is currently in.
     */
    dubbingStatus?: ElevenLabs.DubbingListRequestDubbingStatus;
    /**
     * Filters who created the resources being listed, whether it was the user running the request or someone else that shared the resource with them.
     */
    filterByCreator?: ElevenLabs.DubbingListRequestFilterByCreator;
}
