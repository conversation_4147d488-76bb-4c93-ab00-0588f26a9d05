/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as core from "../../../../../core";
/**
 * @example
 *     {}
 */
export interface BodyDubAVideoOrAnAudioFileV1DubbingPost {
    file?: core.FileLike | undefined;
    csvFile?: core.FileLike | undefined;
    foregroundAudioFile?: core.FileLike | undefined;
    backgroundAudioFile?: core.FileLike | undefined;
    /** Name of the dubbing project. */
    name?: string;
    /** URL of the source video/audio file. */
    sourceUrl?: string;
    /** Source language. */
    sourceLang?: string;
    /** The Target language to dub the content into. */
    targetLang?: string;
    /** [Experimental] An accent to apply when selecting voices from the library and to use to inform translation of the dialect to prefer. */
    targetAccent?: string;
    /** Number of speakers to use for the dubbing. Set to 0 to automatically detect the number of speakers */
    numSpeakers?: number;
    /** Whether to apply watermark to the output video. */
    watermark?: boolean;
    /** Start time of the source video/audio file. */
    startTime?: number;
    /** End time of the source video/audio file. */
    endTime?: number;
    /** Whether to use the highest resolution available. */
    highestResolution?: boolean;
    /** An advanced setting. Whether to drop background audio from the final dub. This can improve dub quality where it's known that audio shouldn't have a background track such as for speeches or monologues. */
    dropBackgroundAudio?: boolean;
    /** [BETA] Whether transcripts should have profanities censored with the words '[censored]' */
    useProfanityFilter?: boolean;
    /** Whether to prepare dub for edits in dubbing studio or edits as a dubbing resource. */
    dubbingStudio?: boolean;
    /** [BETA] Instead of using a voice clone in dubbing, use a similar voice from the ElevenLabs Voice Library. */
    disableVoiceCloning?: boolean;
    /** automatic or manual. Manual mode is only supported when creating a dubbing studio project */
    mode?: string;
    /** Frames per second to use when parsing a CSV file for dubbing. If not provided, FPS will be inferred from timecodes. */
    csvFps?: number;
}
