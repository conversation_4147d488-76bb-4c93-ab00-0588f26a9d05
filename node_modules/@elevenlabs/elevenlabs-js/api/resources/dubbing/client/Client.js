"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dubbing = void 0;
const environments = __importStar(require("../../../../environments"));
const core = __importStar(require("../../../../core"));
const ElevenLabs = __importStar(require("../../../index"));
const serializers = __importStar(require("../../../../serialization/index"));
const headers_1 = require("../../../../core/headers");
const errors = __importStar(require("../../../../errors/index"));
const Client_1 = require("../resources/resource/client/Client");
const Client_2 = require("../resources/audio/client/Client");
const Client_3 = require("../resources/transcript/client/Client");
class Dubbing {
    constructor(_options = {}) {
        this._options = _options;
    }
    get resource() {
        var _a;
        return ((_a = this._resource) !== null && _a !== void 0 ? _a : (this._resource = new Client_1.Resource(this._options)));
    }
    get audio() {
        var _a;
        return ((_a = this._audio) !== null && _a !== void 0 ? _a : (this._audio = new Client_2.Audio(this._options)));
    }
    get transcript() {
        var _a;
        return ((_a = this._transcript) !== null && _a !== void 0 ? _a : (this._transcript = new Client_3.Transcript(this._options)));
    }
    /**
     * List the dubs you have access to.
     *
     * @param {ElevenLabs.DubbingListRequest} request
     * @param {Dubbing.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.dubbing.list()
     */
    list(request = {}, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__list(request, requestOptions));
    }
    __list() {
        return __awaiter(this, arguments, void 0, function* (request = {}, requestOptions) {
            var _a, _b, _c;
            const { cursor, pageSize, dubbingStatus, filterByCreator } = request;
            const _queryParams = {};
            if (cursor != null) {
                _queryParams["cursor"] = cursor;
            }
            if (pageSize != null) {
                _queryParams["page_size"] = pageSize.toString();
            }
            if (dubbingStatus != null) {
                _queryParams["dubbing_status"] = serializers.DubbingListRequestDubbingStatus.jsonOrThrow(dubbingStatus, {
                    unrecognizedObjectKeys: "strip",
                });
            }
            if (filterByCreator != null) {
                _queryParams["filter_by_creator"] = serializers.DubbingListRequestFilterByCreator.jsonOrThrow(filterByCreator, { unrecognizedObjectKeys: "strip" });
            }
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, "v1/dubbing"),
                method: "GET",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                queryParameters: _queryParams,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.DubbingMetadataPageResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling GET /v1/dubbing.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * Dubs a provided audio or video file into given language.
     *
     * @param {ElevenLabs.BodyDubAVideoOrAnAudioFileV1DubbingPost} request
     * @param {Dubbing.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.dubbing.create({})
     */
    create(request, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__create(request, requestOptions));
    }
    __create(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _request = yield core.newFormData();
            if (request.file != null) {
                yield _request.appendFile("file", request.file);
            }
            if (request.csvFile != null) {
                yield _request.appendFile("csv_file", request.csvFile);
            }
            if (request.foregroundAudioFile != null) {
                yield _request.appendFile("foreground_audio_file", request.foregroundAudioFile);
            }
            if (request.backgroundAudioFile != null) {
                yield _request.appendFile("background_audio_file", request.backgroundAudioFile);
            }
            if (request.name != null) {
                _request.append("name", request.name);
            }
            if (request.sourceUrl != null) {
                _request.append("source_url", request.sourceUrl);
            }
            if (request.sourceLang != null) {
                _request.append("source_lang", request.sourceLang);
            }
            if (request.targetLang != null) {
                _request.append("target_lang", request.targetLang);
            }
            if (request.targetAccent != null) {
                _request.append("target_accent", request.targetAccent);
            }
            if (request.numSpeakers != null) {
                _request.append("num_speakers", request.numSpeakers.toString());
            }
            if (request.watermark != null) {
                _request.append("watermark", request.watermark.toString());
            }
            if (request.startTime != null) {
                _request.append("start_time", request.startTime.toString());
            }
            if (request.endTime != null) {
                _request.append("end_time", request.endTime.toString());
            }
            if (request.highestResolution != null) {
                _request.append("highest_resolution", request.highestResolution.toString());
            }
            if (request.dropBackgroundAudio != null) {
                _request.append("drop_background_audio", request.dropBackgroundAudio.toString());
            }
            if (request.useProfanityFilter != null) {
                _request.append("use_profanity_filter", request.useProfanityFilter.toString());
            }
            if (request.dubbingStudio != null) {
                _request.append("dubbing_studio", request.dubbingStudio.toString());
            }
            if (request.disableVoiceCloning != null) {
                _request.append("disable_voice_cloning", request.disableVoiceCloning.toString());
            }
            if (request.mode != null) {
                _request.append("mode", request.mode);
            }
            if (request.csvFps != null) {
                _request.append("csv_fps", request.csvFps.toString());
            }
            const _maybeEncodedRequest = yield _request.getRequest();
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, "v1/dubbing"),
                method: "POST",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)(Object.assign({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }, _maybeEncodedRequest.headers)), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                requestType: "file",
                duplex: _maybeEncodedRequest.duplex,
                body: _maybeEncodedRequest.body,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.DoDubbingResponse.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling POST /v1/dubbing.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * Returns metadata about a dubbing project, including whether it's still in progress or not
     *
     * @param {string} dubbingId - ID of the dubbing project.
     * @param {Dubbing.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.dubbing.get("dubbing_id")
     */
    get(dubbingId, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__get(dubbingId, requestOptions));
    }
    __get(dubbingId, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, `v1/dubbing/${encodeURIComponent(dubbingId)}`),
                method: "GET",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.DubbingMetadataResponse.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling GET /v1/dubbing/{dubbing_id}.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * Deletes a dubbing project.
     *
     * @param {string} dubbingId - ID of the dubbing project.
     * @param {Dubbing.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.dubbing.delete("dubbing_id")
     */
    delete(dubbingId, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__delete(dubbingId, requestOptions));
    }
    __delete(dubbingId, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: core.url.join((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.ElevenLabsEnvironment.Production, `v1/dubbing/${encodeURIComponent(dubbingId)}`),
                method: "DELETE",
                headers: (0, headers_1.mergeHeaders)((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, (0, headers_1.mergeOnlyDefinedHeaders)({ "xi-api-key": requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiKey }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 240000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: serializers.DeleteDubbingResponseModel.parseOrThrow(_response.body, {
                        unrecognizedObjectKeys: "passthrough",
                        allowUnrecognizedUnionMembers: true,
                        allowUnrecognizedEnumValues: true,
                        breadcrumbsPrefix: ["response"],
                    }),
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 422:
                        throw new ElevenLabs.UnprocessableEntityError(serializers.HttpValidationError.parseOrThrow(_response.error.body, {
                            unrecognizedObjectKeys: "passthrough",
                            allowUnrecognizedUnionMembers: true,
                            allowUnrecognizedEnumValues: true,
                            breadcrumbsPrefix: ["response"],
                        }), _response.rawResponse);
                    default:
                        throw new errors.ElevenLabsError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.ElevenLabsError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.ElevenLabsTimeoutError("Timeout exceeded when calling DELETE /v1/dubbing/{dubbing_id}.");
                case "unknown":
                    throw new errors.ElevenLabsError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
}
exports.Dubbing = Dubbing;
