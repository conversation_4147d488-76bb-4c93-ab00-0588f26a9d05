# ElevenLabs Speech-to-Text Proof of Concept

This directory contains a proof-of-concept script that demonstrates speech-to-text transcription using the ElevenLabs SDK, with output formatted as chat-like text bubbles.

## Files

- `speech-to-text-poc.ts` - Main TypeScript script for speech-to-text transcription
- `test-call-recording-1.wav` - Audio file to be transcribed (Dutch conversation)
- `run-examples.ts` - Example runner showing different bubble grouping strategies
- `README.md` - This documentation file

## Features

### 🎤 Speech-to-Text Transcription

- Uses ElevenLabs `@elevenlabs/elevenlabs-js` SDK
- Transcribes WAV audio files to text with word-level timestamps
- Handles different API response formats gracefully
- Comprehensive error handling for file access and API calls

### 💬 Chat Bubble Display

- Groups words into chat-like bubbles based on silence detection
- Configurable silence threshold (default: 500ms)
- Visual formatting with borders and timestamps
- Word wrapping for readability
- Summary statistics including total duration and word count

### 🔧 Technical Features

- Strongly typed TypeScript with proper interfaces
- Environment variable configuration
- Modular function design
- Comprehensive logging and error messages
- Bun runtime optimized

## Prerequisites

1. **ElevenLabs API Key**: Set the `ELEVENLABS_API_KEY` environment variable
2. **Audio File**: Ensure `test-call-recording-1.wav` exists in this directory
3. **Dependencies**: The `@elevenlabs/elevenlabs-js` package (already installed)

## ✅ Proof of Concept Results

The PoC has been successfully implemented and tested! Here's what it achieves:

- **Successfully transcribes** the Dutch audio file using ElevenLabs SDK
- **Creates 22 meaningful chat bubbles** from a 24-second conversation
- **Intelligently groups words** based on natural speech patterns, punctuation, and silence detection
- **Provides comprehensive statistics** including duration, word count, and bubble analysis
- **Handles different speech patterns** including short interjections ("O.", "Daar") and longer phrases

### Sample Output

```
🗨️  TRANSCRIPTION CHAT BUBBLES
┌─ Bubble 1 ─ 00:02 - 00:03 (1.4s, 3 words) ─┐
│ Goedemiddag,   NewFysic.                    │
└─────────────────────────────────────────────┘

┌─ Bubble 2 ─ 00:03 - 00:05 (1.0s, 8 words) ─┐
│ U   spreekt   met   Lieven.                 │
└─────────────────────────────────────────────┘
```

## Usage

### Quick Start

```bash
# From the project root directory
bun run src/playground/speech-to-text-poc.ts
```

### Alternative Methods

```bash
# Navigate to the playground directory
cd src/playground

# Run the script directly
bun speech-to-text-poc.ts

# Or make it executable and run
chmod +x speech-to-text-poc.ts
./speech-to-text-poc.ts
```

## Configuration

The script includes several configurable constants at the top:

```typescript
const AUDIO_FILE_PATH = path.join(__dirname, 'test-call-recording-1.wav');
const SILENCE_THRESHOLD_MS = 500; // Adjust to change bubble grouping
const MODEL_ID = 'scribe_v1'; // ElevenLabs speech-to-text model
```

### Silence Threshold

The `SILENCE_THRESHOLD_MS` parameter controls how words are grouped into bubbles:

- **Lower values (100-300ms)**: More bubbles, shorter phrases
- **Higher values (800-1500ms)**: Fewer bubbles, longer phrases
- **Default (500ms)**: Balanced grouping for natural conversation flow

## Output Format

The script produces output in several sections:

### 1. Processing Status

```
🚀 ElevenLabs Speech-to-Text Proof of Concept
==================================================
✅ ElevenLabs client initialized
✅ Loaded audio file: /path/to/test-call-recording-1.wav (1234567 bytes)
🎤 Starting transcription...
✅ Transcription completed successfully
```

### 2. Full Transcription

```
📝 Full transcription text:
"Hello, this is a test recording for the speech-to-text functionality..."

🔤 Detected 45 words with timestamps
```

### 3. Chat Bubbles

```
================================================================================
🗨️  TRANSCRIPTION CHAT BUBBLES
================================================================================

┌─ Bubble 1 ─ 00:01 - 00:03 (2.1s, 8 words) ─┐
│ Hello, this is a test recording for the                                    │
└──────────────────────────────────────────────────────────────────────────┘

┌─ Bubble 2 ─ 00:04 - 00:06 (1.8s, 6 words) ─┐
│ speech-to-text functionality demonstration.                                │
└──────────────────────────────────────────────────────────────────────────┘
```

### 4. Summary Statistics

```
================================================================================
📊 SUMMARY STATISTICS
================================================================================
Total bubbles: 12
Total duration: 01:23
Total words: 156
Average words per bubble: 13.0
Silence threshold: 500ms
```

## Error Handling

The script includes comprehensive error handling for common issues:

- **Missing API Key**: Clear message about setting `ELEVENLABS_API_KEY`
- **File Not Found**: Specific error if audio file doesn't exist
- **API Errors**: Detailed transcription failure messages
- **Format Issues**: Handles different API response formats
- **Network Issues**: Graceful handling of connection problems

## API Response Formats

The script handles multiple ElevenLabs API response formats:

1. **Standard Format**: `{ text: string, words: TranscriptionWord[] }`
2. **Alternative Format**: `{ transcripts: [{ text: string, words: TranscriptionWord[] }] }`
3. **Async Format**: `{ message: string, requestId: string }` (for webhook processing)

## Customization

### Adding New Features

The modular design makes it easy to extend functionality:

```typescript
// Add speaker detection
interface ChatBubble {
	text: string;
	startTime: number;
	endTime: number;
	wordCount: number;
	speaker?: string; // Add speaker identification
}

// Add confidence scoring
function filterLowConfidenceWords(words: TranscriptionWord[]): TranscriptionWord[] {
	return words.filter(word => (word.confidence || 1) > 0.7);
}

// Add export functionality
function exportToChatFormat(bubbles: ChatBubble[]): string {
	return bubbles.map(bubble => `[${formatTime(bubble.startTime)}] ${bubble.text}`).join('\n');
}
```

### Performance Optimization

For large audio files, consider:

- Streaming processing for real-time transcription
- Chunking large files into smaller segments
- Caching transcription results
- Parallel processing for multiple files

## Troubleshooting

### Common Issues

1. **"ELEVENLABS_API_KEY environment variable is required"**
   - Ensure your `.env` file contains: `ELEVENLABS_API_KEY=your_api_key_here`
   - Or set it in your shell: `export ELEVENLABS_API_KEY=your_api_key_here`

2. **"Audio file not found"**
   - Verify `test-call-recording-1.wav` exists in `src/playground/`
   - Check file permissions and path

3. **"Transcription failed"**
   - Verify your API key is valid and has sufficient credits
   - Check audio file format (WAV, MP3, etc.)
   - Ensure stable internet connection

4. **Empty or poor transcription results**
   - Check audio quality and volume levels
   - Verify the audio contains clear speech
   - Try adjusting the model ID if available

### Debug Mode

Add debug logging by modifying the script:

```typescript
const DEBUG = true;

function debugLog(message: string, data?: any): void {
	if (DEBUG) {
		console.log(`🐛 DEBUG: ${message}`, data || '');
	}
}
```

## Next Steps

This proof-of-concept can be extended with:

- **Real-time transcription** for live audio streams
- **Speaker diarization** to identify different speakers
- **Sentiment analysis** of transcribed text
- **Export formats** (JSON, SRT, VTT, etc.)
- **Web interface** for file upload and visualization
- **Batch processing** for multiple audio files
- **Integration** with other AI services for analysis

## License

This proof-of-concept is part of the transcribe-webapp project and follows the same licensing terms.
