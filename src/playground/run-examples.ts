#!/usr/bin/env bun

/**
 * Example runner for different speech-to-text configurations
 *
 * This script demonstrates how to run the speech-to-text PoC with different
 * bubble grouping strategies and configurations.
 */

import { spawn } from 'node:child_process';
import * as path from 'node:path';

const SCRIPT_PATH = path.join(__dirname, 'speech-to-text-poc.ts');

interface RunConfig {
	name: string;
	description: string;
	silenceThreshold: number;
	maxWordsPerBubble: number;
}

const configurations: RunConfig[] = [
	{
		name: 'Fine-grained',
		description: 'Very short bubbles, captures every pause',
		silenceThreshold: 100,
		maxWordsPerBubble: 15
	},
	{
		name: 'Balanced',
		description: 'Natural conversation flow (default)',
		silenceThreshold: 200,
		maxWordsPerBubble: 25
	},
	{
		name: 'Coarse',
		description: 'Longer bubbles, fewer interruptions',
		silenceThreshold: 500,
		maxWordsPerBubble: 40
	}
];

async function runConfiguration(config: RunConfig): Promise<void> {
	console.log(`\n${'='.repeat(80)}`);
	console.log(`🔧 CONFIGURATION: ${config.name.toUpperCase()}`);
	console.log(`📝 ${config.description}`);
	console.log(`⏱️  Silence threshold: ${config.silenceThreshold}ms`);
	console.log(`📊 Max words per bubble: ${config.maxWordsPerBubble}`);
	console.log(`${'='.repeat(80)}\n`);

	return new Promise((resolve, reject) => {
		// Create a temporary modified script with the new configuration
		const process = spawn('bun', ['run', SCRIPT_PATH], {
			stdio: 'inherit',
			env: {
				...process.env,
				SILENCE_THRESHOLD_MS: config.silenceThreshold.toString(),
				MAX_WORDS_PER_BUBBLE: config.maxWordsPerBubble.toString()
			}
		});

		process.on('close', code => {
			if (code === 0) {
				resolve();
			} else {
				reject(new Error(`Process exited with code ${code}`));
			}
		});

		process.on('error', error => {
			reject(error);
		});
	});
}

async function main(): Promise<void> {
	console.log('🚀 Speech-to-Text Configuration Examples');
	console.log('This will run the same audio file with different bubble grouping strategies.\n');

	for (const config of configurations) {
		try {
			await runConfiguration(config);
			console.log(`\n✅ Completed: ${config.name}\n`);

			// Add a pause between configurations
			console.log('Press Enter to continue to the next configuration...');
			await new Promise(resolve => {
				process.stdin.once('data', () => resolve(void 0));
			});
		} catch (error) {
			console.error(`❌ Failed to run ${config.name}:`, error);
		}
	}

	console.log('🎉 All configurations completed!');
}

// Execute the script
if (import.meta.main) {
	void main();
}
