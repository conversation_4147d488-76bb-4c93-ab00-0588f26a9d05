#!/usr/bin/env bun

/**
 * ElevenLabs Speech-to-Text Proof of Concept
 *
 * This script demonstrates speech-to-text transcription using the ElevenLabs SDK
 * and displays the results as chat-like text bubbles grouped by silences.
 *
 * Requirements:
 * - ELEVENLABS_API_KEY environment variable must be set
 * - Audio file: src/playground/test-call-recording-1.wav
 *
 * Usage:
 *   bun run src/playground/speech-to-text-poc.ts
 */

import * as fs from 'node:fs';
import * as path from 'node:path';

import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';

// Configuration constants
const AUDIO_FILE_PATH = path.join(__dirname, 'test-call-recording-1.wav');
const SILENCE_THRESHOLD_MS = 200; // Group words separated by more than 200ms into different bubbles
const MAX_WORDS_PER_BUBBLE = 25; // Maximum words per bubble for readability
const SENTENCE_BREAK_WORDS = new Set(['.', '!', '?', 'O.', 'Oké,', 'Nou,', 'Dat', 'Daar']); // Words that suggest sentence breaks
const MODEL_ID = 'scribe_v1'; // ElevenLabs speech-to-text model

// Types for transcription response (matching ElevenLabs API)
interface TranscriptionWord {
	text: string; // ElevenLabs uses 'text' not 'word'
	start: number;
	end: number;
	confidence?: number;
}

interface TranscriptionResponse {
	text: string;
	words: TranscriptionWord[];
}

interface ChatBubble {
	text: string;
	startTime: number;
	endTime: number;
	wordCount: number;
}

/**
 * Initialize the ElevenLabs client with API key from environment
 */
function createElevenLabsClient(): ElevenLabsClient {
	const apiKey = process.env.ELEVENLABS_API_KEY;

	if (!apiKey) {
		throw new Error(
			'ELEVENLABS_API_KEY environment variable is required. ' +
				'Please set it in your .env file or environment.'
		);
	}

	return new ElevenLabsClient({
		apiKey: apiKey
	});
}

/**
 * Load and validate the audio file
 */
function loadAudioFile(filePath: string): Buffer {
	try {
		if (!fs.existsSync(filePath)) {
			throw new Error(`Audio file not found: ${filePath}`);
		}

		const audioBuffer = fs.readFileSync(filePath);
		console.log(`✅ Loaded audio file: ${filePath} (${audioBuffer.length} bytes)`);
		return audioBuffer;
	} catch (error) {
		throw new Error(
			`Failed to load audio file: ${error instanceof Error ? error.message : String(error)}`
		);
	}
}

/**
 * Transcribe audio using ElevenLabs speech-to-text API
 */
async function transcribeAudio(
	client: ElevenLabsClient,
	audioBuffer: Buffer
): Promise<TranscriptionResponse> {
	try {
		console.log('🎤 Starting transcription...');

		// Create a Blob from the audio buffer (required by the API)
		const audioBlob = new Blob([new Uint8Array(audioBuffer)], { type: 'audio/wav' });

		// Call the speech-to-text API
		const transcription = await client.speechToText.convert({
			file: audioBlob,
			modelId: MODEL_ID
		});

		// Handle different response formats based on the API documentation
		if ('text' in transcription && 'words' in transcription) {
			console.log('✅ Transcription completed successfully');
			// Map the API response to our interface
			const mappedWords: TranscriptionWord[] = transcription.words.map(word => ({
				text: word.text || '',
				start: word.start || 0,
				end: word.end || 0,
				confidence: 1 // ElevenLabs doesn't provide confidence scores
			}));
			return {
				text: transcription.text,
				words: mappedWords
			};
		} else if ('transcripts' in transcription && Array.isArray(transcription.transcripts)) {
			// Handle alternative response format
			const firstTranscript = transcription.transcripts[0];
			if (firstTranscript && 'text' in firstTranscript) {
				console.log('✅ Transcription completed successfully (alternative format)');
				const mappedWords: TranscriptionWord[] = (firstTranscript.words || []).map(word => ({
					text: word.text || '',
					start: word.start || 0,
					end: word.end || 0,
					confidence: 1 // ElevenLabs doesn't provide confidence scores
				}));
				return {
					text: firstTranscript.text,
					words: mappedWords
				};
			}
		} else if ('message' in transcription) {
			// Handle async processing response
			throw new Error(`Transcription is being processed asynchronously: ${transcription.message}`);
		}

		throw new Error('Unexpected transcription response format');
	} catch (error) {
		throw new Error(
			`Transcription failed: ${error instanceof Error ? error.message : String(error)}`
		);
	}
}

/**
 * Group words into chat bubbles based on silence gaps
 */
function groupWordsIntoBubbles(words: TranscriptionWord[]): ChatBubble[] {
	if (words.length === 0) {
		return [];
	}

	const bubbles: ChatBubble[] = [];
	let currentBubble: TranscriptionWord[] = [words[0]!];

	for (let i = 1; i < words.length; i++) {
		const currentWord = words[i]!;
		const previousWord = words[i - 1]!;

		// Calculate the gap between the end of the previous word and start of current word
		const silenceGap = (currentWord.start - previousWord.end) * 1_000; // Convert to milliseconds

		// Check if we should break into a new bubble based on:
		// 1. Silence threshold
		// 2. Maximum words per bubble
		// 3. Natural sentence breaks
		const shouldBreak =
			silenceGap > SILENCE_THRESHOLD_MS ||
			currentBubble.length >= MAX_WORDS_PER_BUBBLE ||
			SENTENCE_BREAK_WORDS.has(currentWord.text) ||
			previousWord.text.endsWith('.') ||
			previousWord.text.endsWith('!') ||
			previousWord.text.endsWith('?');

		if (shouldBreak) {
			// Create a bubble from the current group of words
			bubbles.push(createBubbleFromWords(currentBubble));
			currentBubble = [currentWord];
		} else {
			currentBubble.push(currentWord);
		}
	}

	// Add the final bubble
	if (currentBubble.length > 0) {
		bubbles.push(createBubbleFromWords(currentBubble));
	}

	return bubbles;
}

/**
 * Create a chat bubble from a group of words
 */
function createBubbleFromWords(words: TranscriptionWord[]): ChatBubble {
	const text = words.map(w => w.text).join(' ');
	const startTime = words[0]!.start;
	const endTime = words.at(-1)!.end;

	return {
		text,
		startTime,
		endTime,
		wordCount: words.length
	};
}

/**
 * Format time in seconds to MM:SS format
 */
function formatTime(seconds: number): string {
	const minutes = Math.floor(seconds / 60);
	const remainingSeconds = Math.floor(seconds % 60);
	return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Display chat bubbles in a visually appealing format
 */
function displayChatBubbles(bubbles: ChatBubble[]): void {
	console.log('\n' + '='.repeat(80));
	console.log('🗨️  TRANSCRIPTION CHAT BUBBLES');
	console.log('='.repeat(80));

	if (bubbles.length === 0) {
		console.log('No speech detected in the audio file.');
		return;
	}

	for (const [index, bubble] of bubbles.entries()) {
		const timeRange = `${formatTime(bubble.startTime)} - ${formatTime(bubble.endTime)}`;
		const duration = (bubble.endTime - bubble.startTime).toFixed(1);

		console.log(
			`\n┌─ Bubble ${index + 1} ─ ${timeRange} (${duration}s, ${bubble.wordCount} words) ─┐`
		);

		// Word wrap the text to fit nicely in the bubble
		const maxWidth = 70;
		const words = bubble.text.split(' ');
		let currentLine = '';

		for (const word of words) {
			if (currentLine.length + word.length + 1 <= maxWidth) {
				currentLine += (currentLine ? ' ' : '') + word;
			} else {
				if (currentLine) {
					console.log(`│ ${currentLine.padEnd(maxWidth)} │`);
				}
				currentLine = word;
			}
		}

		if (currentLine) {
			console.log(`│ ${currentLine.padEnd(maxWidth)} │`);
		}

		console.log(`└${'─'.repeat(78)}┘`);
	}

	// Display summary statistics
	const totalDuration = bubbles.at(-1)!.endTime - bubbles[0]!.startTime;
	const totalWords = bubbles.reduce((sum, bubble) => sum + bubble.wordCount, 0);
	const averageWordsPerBubble = (totalWords / bubbles.length).toFixed(1);

	console.log('\n' + '='.repeat(80));
	console.log('📊 SUMMARY STATISTICS');
	console.log('='.repeat(80));
	console.log(`Total bubbles: ${bubbles.length}`);
	console.log(`Total duration: ${formatTime(totalDuration)}`);
	console.log(`Total words: ${totalWords}`);
	console.log(`Average words per bubble: ${averageWordsPerBubble}`);
	console.log(`Silence threshold: ${SILENCE_THRESHOLD_MS}ms`);
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
	try {
		console.log('🚀 ElevenLabs Speech-to-Text Proof of Concept');
		console.log('='.repeat(50));

		// Initialize client
		const client = createElevenLabsClient();
		console.log('✅ ElevenLabs client initialized');

		// Load audio file
		const audioBuffer = loadAudioFile(AUDIO_FILE_PATH);

		// Transcribe audio
		const transcription = await transcribeAudio(client, audioBuffer);

		console.log(`\n📝 Full transcription text:\n"${transcription.text}"\n`);
		console.log(`🔤 Detected ${transcription.words.length} words with timestamps`);

		// Group words into chat bubbles
		const bubbles = groupWordsIntoBubbles(transcription.words);

		// Display results
		displayChatBubbles(bubbles);

		console.log('\n✅ Transcription completed successfully!');
	} catch (error) {
		console.error('\n❌ Error:', error instanceof Error ? error.message : String(error));
		process.exit(1);
	}
}

// Execute the script
if (import.meta.main) {
	void main();
}
