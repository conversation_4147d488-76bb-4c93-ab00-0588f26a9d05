#!/usr/bin/env bun

/**
 * ElevenLabs Speech-to-Text Proof of Concept
 *
 * This script demonstrates speech-to-text transcription using the ElevenLabs SDK
 * and displays the results as chat-like text bubbles grouped by silences.
 *
 * Requirements:
 * - ELEVENLABS_API_KEY environment variable must be set
 * - Audio file: src/playground/test-call-recording-1.wav
 *
 * Usage:
 *   bun run src/playground/speech-to-text-poc.ts
 */

import fs from 'node:fs';
import path from 'node:path';

import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';

// Configuration constants
const AUDIO_FILE_PATH = path.join(import.meta.dirname, 'test-call-recording-1.wav');
const SILENCE_THRESHOLD_MS = 200; // Group words separated by more than 200ms into different bubbles
const MAX_WORDS_PER_BUBBLE = 25; // Maximum words per bubble for readability
const SENTENCE_BREAK_WORDS = new Set(['.', '!', '?', 'O.', 'Ok<PERSON>,', 'Nou,', 'Dat', 'Daar']); // Words that suggest sentence breaks
const MODEL_ID = 'scribe_v1'; // ElevenLabs speech-to-text model

// Types for transcription response (matching ElevenLabs API)
interface TranscriptionWord {
	text: string; // ElevenLabs uses 'text' not 'word'
	start: number;
	end: number;
	confidence?: number;
	speaker_id?: string; // ElevenLabs provides speaker identification
	type?: 'word' | 'spacing'; // Word type from ElevenLabs API
}

interface TranscriptionResponse {
	text: string;
	words: TranscriptionWord[];
}

interface ChatBubble {
	text: string;
	startTime: number;
	endTime: number;
	wordCount: number;
	speakerId: string;
	speakerLabel: string; // Human-readable speaker label (Caller/Callee)
}

interface SpeakerStats {
	speakerId: string;
	speakerLabel: string;
	totalWords: number;
	totalDuration: number;
	bubbleCount: number;
}

/**
 * Initialize the ElevenLabs client with API key from environment
 */
function createElevenLabsClient(): ElevenLabsClient {
	const apiKey = process.env.ELEVENLABS_API_KEY;

	if (!apiKey) {
		throw new Error(
			'ELEVENLABS_API_KEY environment variable is required. ' +
				'Please set it in your .env file or environment.'
		);
	}

	return new ElevenLabsClient({
		apiKey: apiKey
	});
}

/**
 * Load and validate the audio file
 */
function loadAudioFile(filePath: string): Buffer {
	try {
		if (!fs.existsSync(filePath)) {
			throw new Error(`Audio file not found: ${filePath}`);
		}

		const audioBuffer = fs.readFileSync(filePath);
		console.log(`✅ Loaded audio file: ${filePath} (${audioBuffer.length} bytes)`);
		return audioBuffer;
	} catch (error) {
		throw new Error(
			`Failed to load audio file: ${error instanceof Error ? error.message : String(error)}`
		);
	}
}

/**
 * Transcribe audio using ElevenLabs speech-to-text API
 */
async function transcribeAudio(
	client: ElevenLabsClient,
	audioBuffer: Buffer
): Promise<TranscriptionResponse> {
	try {
		console.log('🎤 Starting transcription...');

		// Create a Blob from the audio buffer (required by the API)
		const audioBlob = new Blob([new Uint8Array(audioBuffer)], { type: 'audio/wav' });

		// Call the speech-to-text API
		const transcription = await client.speechToText.convert({
			file: audioBlob,
			modelId: MODEL_ID
		});

		// Handle different response formats based on the API documentation
		if ('text' in transcription && 'words' in transcription) {
			console.log('✅ Transcription completed successfully');
			// Map the API response to our interface
			const mappedWords: TranscriptionWord[] = transcription.words
				.filter(word => word.type !== 'spacing') // Filter out spacing tokens
				.map(word => ({
					text: word.text ?? '',
					start: word.start ?? 0,
					end: word.end ?? 0,
					confidence: 1, // ElevenLabs doesn't provide confidence scores
					speaker_id: (word as any).speakerId ?? (word as any).speaker_id ?? 'speaker_0',
					type: word.type as 'word' | 'spacing'
				}));
			return {
				text: transcription.text,
				words: mappedWords
			};
		} else if ('transcripts' in transcription && Array.isArray(transcription.transcripts)) {
			// Handle alternative response format
			const firstTranscript = transcription.transcripts[0];
			if (firstTranscript && 'text' in firstTranscript) {
				console.log('✅ Transcription completed successfully (alternative format)');
				const mappedWords: TranscriptionWord[] = (firstTranscript.words ?? []).map(word => ({
					text: word.text ?? '',
					start: word.start ?? 0,
					end: word.end ?? 0,
					confidence: 1, // ElevenLabs doesn't provide confidence scores
					speaker_id: (word as any).speakerId ?? (word as any).speaker_id ?? 'speaker_0',
					type: word.type as 'word' | 'spacing'
				}));
				return {
					text: firstTranscript.text,
					words: mappedWords
				};
			}
		} else if ('message' in transcription) {
			// Handle async processing response
			throw new Error(`Transcription is being processed asynchronously: ${transcription.message}`);
		}

		throw new Error('Unexpected transcription response format');
	} catch (error) {
		throw new Error(
			`Transcription failed: ${error instanceof Error ? error.message : String(error)}`
		);
	}
}

/**
 * Group words into chat bubbles based on silence gaps and speaker changes
 */
function groupWordsIntoBubbles(words: TranscriptionWord[]): ChatBubble[] {
	if (words.length === 0) {
		return [];
	}

	// Apply speaker simulation if only one speaker detected (for demo purposes)
	const processedWords = simulateTwoSpeakersIfNeeded(words);

	const bubbles: ChatBubble[] = [];
	let currentBubble: TranscriptionWord[] = [processedWords[0]!];

	for (let i = 1; i < processedWords.length; i++) {
		const currentWord = processedWords[i]!;
		const previousWord = processedWords[i - 1]!;

		// Calculate the gap between the end of the previous word and start of current word
		const silenceGap = (currentWord.start - previousWord.end) * 1_000; // Convert to milliseconds

		// Check if we should break into a new bubble based on:
		// 1. Silence threshold
		// 2. Maximum words per bubble
		// 3. Natural sentence breaks
		// 4. Speaker changes
		const shouldBreak =
			silenceGap > SILENCE_THRESHOLD_MS ||
			currentBubble.length >= MAX_WORDS_PER_BUBBLE ||
			SENTENCE_BREAK_WORDS.has(currentWord.text) ||
			previousWord.text.endsWith('.') ||
			previousWord.text.endsWith('!') ||
			previousWord.text.endsWith('?') ||
			currentWord.speaker_id !== previousWord.speaker_id; // Break on speaker change

		if (shouldBreak) {
			// Create a bubble from the current group of words
			bubbles.push(createBubbleFromWords(currentBubble));
			currentBubble = [currentWord];
		} else {
			currentBubble.push(currentWord);
		}
	}

	// Add the final bubble
	if (currentBubble.length > 0) {
		bubbles.push(createBubbleFromWords(currentBubble));
	}

	return bubbles;
}

/**
 * Simulate two speakers if only one speaker is detected (for demonstration)
 * This helps show the two-speaker layout even with single-speaker audio
 */
function simulateTwoSpeakersIfNeeded(words: TranscriptionWord[]): TranscriptionWord[] {
	// Check if we already have multiple speakers
	const uniqueSpeakers = new Set(words.map(word => word.speaker_id));
	if (uniqueSpeakers.size > 1) {
		return words; // Already have multiple speakers, return as-is
	}

	console.log('🎭 Single speaker detected - simulating two-speaker conversation for demo');

	// Create a copy of words and alternate speakers based on sentence boundaries
	const processedWords = [...words];
	let currentSpeaker = 'speaker_0';

	for (const processedWord of processedWords) {
		const word = processedWord;

		// Switch speaker on sentence endings or specific transition words
		if (
			word.text.endsWith('.') ||
			word.text.endsWith('!') ||
			word.text.endsWith('?') ||
			['O,', 'Oké,', 'Nou,', 'Daar'].includes(word.text)
		) {
			// Switch to the other speaker for the next sentence
			currentSpeaker = currentSpeaker === 'speaker_0' ? 'speaker_1' : 'speaker_0';
		}

		word.speaker_id = currentSpeaker;
	}

	return processedWords;
}

/**
 * Create a chat bubble from a group of words
 */
function createBubbleFromWords(words: TranscriptionWord[]): ChatBubble {
	const text = words.map(w => w.text).join(' ');
	const startTime = words[0]!.start;
	const endTime = words.at(-1)!.end;
	const speakerId = words[0]!.speaker_id ?? 'speaker_0';

	// Map speaker IDs to human-readable labels
	const speakerLabel = getSpeakerLabel(speakerId);

	return {
		text,
		startTime,
		endTime,
		wordCount: words.length,
		speakerId,
		speakerLabel
	};
}

/**
 * Map speaker ID to human-readable label
 */
function getSpeakerLabel(speakerId: string): string {
	switch (speakerId) {
		case 'speaker_0': {
			return 'Caller';
		}
		case 'speaker_1': {
			return 'Callee';
		}
		default: {
			return `Speaker ${speakerId.replace('speaker_', '')}`;
		}
	}
}

/**
 * Format time in seconds to MM:SS format
 */
function formatTime(seconds: number): string {
	const minutes = Math.floor(seconds / 60);
	const remainingSeconds = Math.floor(seconds % 60);
	return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Display chat bubbles in a two-speaker phone call format
 */
function displayChatBubbles(bubbles: ChatBubble[]): void {
	console.log('\n' + '='.repeat(100));
	console.log('� PHONE CALL TRANSCRIPTION - TWO SPEAKER VIEW');
	console.log('='.repeat(100));

	if (bubbles.length === 0) {
		console.log('No speech detected in the audio file.');
		return;
	}

	// Display header with speaker labels
	console.log('');
	console.log('👤 CALLER (Left)'.padEnd(50) + '👥 CALLEE (Right)'.padStart(50));
	console.log('─'.repeat(50) + '  ' + '─'.repeat(48));

	for (const [index, bubble] of bubbles.entries()) {
		const timeRange = `${formatTime(bubble.startTime)} - ${formatTime(bubble.endTime)}`;
		const duration = (bubble.endTime - bubble.startTime).toFixed(1);
		const bubbleInfo = `#${index + 1} ${timeRange} (${duration}s)`;

		// Determine if this is caller (left) or callee (right)
		const isCaller = bubble.speakerId === 'speaker_0';

		if (isCaller) {
			// Display caller bubble on the left
			displayLeftBubble(bubble, bubbleInfo);
		} else {
			// Display callee bubble on the right
			displayRightBubble(bubble, bubbleInfo);
		}
	}

	// Display summary statistics with speaker breakdown
	displaySpeakerStatistics(bubbles);
}

/**
 * Display a chat bubble on the left side (Caller)
 */
function displayLeftBubble(bubble: ChatBubble, bubbleInfo: string): void {
	const maxWidth = 45;
	const lines = wrapText(bubble.text, maxWidth);

	console.log('');
	console.log(`┌─ ${bubbleInfo} ─${'─'.repeat(Math.max(0, maxWidth - bubbleInfo.length - 3))}┐`);

	for (const line of lines) {
		console.log(`│ ${line.padEnd(maxWidth)} │`);
	}

	console.log(`└${'─'.repeat(maxWidth + 2)}┘`);
}

/**
 * Display a chat bubble on the right side (Callee)
 */
function displayRightBubble(bubble: ChatBubble, bubbleInfo: string): void {
	const maxWidth = 45;
	const leftPadding = 52; // Space to push bubble to the right
	const lines = wrapText(bubble.text, maxWidth);

	console.log('');
	console.log(
		' '.repeat(leftPadding) +
			`┌─ ${bubbleInfo} ─${'─'.repeat(Math.max(0, maxWidth - bubbleInfo.length - 3))}┐`
	);

	for (const line of lines) {
		console.log(' '.repeat(leftPadding) + `│ ${line.padEnd(maxWidth)} │`);
	}

	console.log(' '.repeat(leftPadding) + `└${'─'.repeat(maxWidth + 2)}┘`);
}

/**
 * Wrap text to fit within specified width
 */
function wrapText(text: string, maxWidth: number): string[] {
	const words = text.split(' ');
	const lines: string[] = [];
	let currentLine = '';

	for (const word of words) {
		if (currentLine.length + word.length + 1 <= maxWidth) {
			currentLine += (currentLine ? ' ' : '') + word;
		} else {
			if (currentLine) {
				lines.push(currentLine);
			}
			currentLine = word;
		}
	}

	if (currentLine) {
		lines.push(currentLine);
	}

	return lines.length > 0 ? lines : [''];
}

/**
 * Display comprehensive statistics including speaker breakdown
 */
function displaySpeakerStatistics(bubbles: ChatBubble[]): void {
	const totalDuration = bubbles.at(-1)!.endTime - bubbles[0]!.startTime;
	const totalWords = bubbles.reduce((sum, bubble) => sum + bubble.wordCount, 0);

	// Calculate speaker-specific statistics
	const speakerStats = new Map<string, SpeakerStats>();

	for (const bubble of bubbles) {
		const stats = speakerStats.get(bubble.speakerId) ?? {
			speakerId: bubble.speakerId,
			speakerLabel: bubble.speakerLabel,
			totalWords: 0,
			totalDuration: 0,
			bubbleCount: 0
		};

		stats.totalWords += bubble.wordCount;
		stats.totalDuration += bubble.endTime - bubble.startTime;
		stats.bubbleCount += 1;

		speakerStats.set(bubble.speakerId, stats);
	}

	console.log('\n' + '='.repeat(100));
	console.log('📊 CONVERSATION STATISTICS');
	console.log('='.repeat(100));

	// Overall statistics
	console.log(`📞 Total conversation duration: ${formatTime(totalDuration)}`);
	console.log(`💬 Total bubbles: ${bubbles.length}`);
	console.log(`📝 Total words: ${totalWords}`);
	console.log(`⚙️  Silence threshold: ${SILENCE_THRESHOLD_MS}ms`);

	// Speaker breakdown
	console.log('\n👥 SPEAKER BREAKDOWN:');
	for (const stats of speakerStats.values()) {
		const percentage = ((stats.totalWords / totalWords) * 100).toFixed(1);
		const avgWordsPerBubble = (stats.totalWords / stats.bubbleCount).toFixed(1);

		console.log(`\n${stats.speakerLabel} (${stats.speakerId}):`);
		console.log(`  💬 Bubbles: ${stats.bubbleCount}`);
		console.log(`  📝 Words: ${stats.totalWords} (${percentage}%)`);
		console.log(`  ⏱️  Speaking time: ${formatTime(stats.totalDuration)}`);
		console.log(`  📊 Avg words/bubble: ${avgWordsPerBubble}`);
	}
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
	try {
		console.log('🚀 ElevenLabs Speech-to-Text Proof of Concept');
		console.log('='.repeat(50));

		// Initialize client
		const client = createElevenLabsClient();
		console.log('✅ ElevenLabs client initialized');

		// Load audio file
		const audioBuffer = loadAudioFile(AUDIO_FILE_PATH);

		// Transcribe audio
		const transcription = await transcribeAudio(client, audioBuffer);

		console.log(`\n📝 Full transcription text:\n"${transcription.text}"\n`);
		console.log(`🔤 Detected ${transcription.words.length} words with timestamps`);

		// Group words into chat bubbles
		const bubbles = groupWordsIntoBubbles(transcription.words);

		// Display results
		displayChatBubbles(bubbles);

		console.log('\n✅ Transcription completed successfully!');
	} catch (error) {
		console.error('\n❌ Error:', error instanceof Error ? error.message : String(error));
		process.exit(1);
	}
}

// Execute the script
if (import.meta.main) {
	void main();
}
